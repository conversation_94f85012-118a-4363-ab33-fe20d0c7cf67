--!strict
--[[
	Gun System Framework - Client Core
	
	This module initializes the client-side GSF systems for FPS gameplay:
	- Viewmodel management for R6 blocky characters
	- Input handling for weapon controls
	- CFrame-based weapon animations
	- Client-side weapon state management
	- FPS camera controls
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import GSF modules
local GSF = require(ReplicatedStorage.GSF.Core)
local ViewmodelSystem = require(script.Parent.GSF_ViewmodelSystem)
local InputManager = require(script.Parent.GSF_InputManager)
local WeaponAnimator = require(script.Parent.GSF_WeaponAnimator)
local FPSCamera = require(script.Parent.GSF_FPSCamera)

-- ============================================================================
-- CLIENT CORE SYSTEM
-- ============================================================================

local ClientCore = {}
ClientCore.__index = ClientCore

-- Client state
local isInitialized = false
local localPlayer = Players.LocalPlayer
local currentWeapon = nil
local viewmodel = nil
local camera = nil

-- Client systems
local systems = {
	viewmodel = nil,
	input = nil,
	animator = nil,
	camera = nil,
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function ClientCore.initialize(): boolean
	if isInitialized then
		warn("[GSF Client] Already initialized")
		return false
	end
	
	print("[GSF Client] Initializing client-side systems...")
	
	-- Wait for character
	local character = localPlayer.Character or localPlayer.CharacterAdded:Wait()
	
	-- Initialize core GSF
	if not GSF.isInitialized then
		GSF.initialize()
	end
	
	-- Initialize client systems
	systems.viewmodel = ViewmodelSystem.new()
	systems.input = InputManager.new()
	systems.animator = WeaponAnimator.new()
	systems.camera = FPSCamera.new()
	
	-- Connect systems
	ClientCore._connectSystems()
	
	-- Set up character respawn handling
	localPlayer.CharacterAdded:Connect(function(newCharacter)
		ClientCore._onCharacterSpawned(newCharacter)
	end)
	
	isInitialized = true
	print("[GSF Client] Client-side systems initialized successfully")
	return true
end

function ClientCore._connectSystems(): ()
	-- Connect input to weapon actions
	systems.input.onFirePressed:Connect(function()
		if currentWeapon then
			ClientCore.fireWeapon()
		end
	end)
	
	systems.input.onReloadPressed:Connect(function()
		if currentWeapon then
			ClientCore.reloadWeapon()
		end
	end)
	
	systems.input.onAimPressed:Connect(function()
		if currentWeapon then
			ClientCore.aimDownSights(true)
		end
	end)
	
	systems.input.onAimReleased:Connect(function()
		if currentWeapon then
			ClientCore.aimDownSights(false)
		end
	end)
	
	-- Connect camera to viewmodel
	systems.camera.onCameraUpdate:Connect(function(cameraCFrame)
		if systems.viewmodel then
			systems.viewmodel:updateCamera(cameraCFrame)
		end
	end)
end

function ClientCore._onCharacterSpawned(character: Model): ()
	print("[GSF Client] Character spawned, updating systems...")
	
	-- Update viewmodel for new character
	if systems.viewmodel then
		systems.viewmodel:setCharacter(character)
	end
	
	-- Update camera for new character
	if systems.camera then
		systems.camera:setCharacter(character)
	end
end

-- ============================================================================
-- WEAPON MANAGEMENT
-- ============================================================================

function ClientCore.equipWeapon(weaponData: { [string]: any }): boolean
	print(`[GSF Client] Equipping weapon: {weaponData.name}`)
	
	-- Create weapon instance
	currentWeapon = GSF.WeaponEntity.create(weaponData)
	if not currentWeapon then
		warn("[GSF Client] Failed to create weapon")
		return false
	end
	
	-- Set up viewmodel
	if systems.viewmodel then
		systems.viewmodel:equipWeapon(currentWeapon)
	end
	
	-- Set up animator
	if systems.animator then
		systems.animator:setWeapon(currentWeapon)
	end
	
	-- Configure camera for weapon
	if systems.camera then
		systems.camera:setWeapon(currentWeapon)
	end
	
	return true
end

function ClientCore.unequipWeapon(): ()
	if not currentWeapon then
		return
	end
	
	print("[GSF Client] Unequipping weapon")
	
	-- Clear viewmodel
	if systems.viewmodel then
		systems.viewmodel:unequipWeapon()
	end
	
	-- Clear animator
	if systems.animator then
		systems.animator:clearWeapon()
	end
	
	-- Reset camera
	if systems.camera then
		systems.camera:clearWeapon()
	end
	
	currentWeapon = nil
end

-- ============================================================================
-- WEAPON ACTIONS
-- ============================================================================

function ClientCore.fireWeapon(): ()
	if not currentWeapon or not systems.camera then
		return
	end
	
	-- Get camera direction
	local cameraCFrame = systems.camera:getCameraCFrame()
	local origin = cameraCFrame.Position
	local direction = cameraCFrame.LookVector
	
	-- Fire weapon
	local fireResult = currentWeapon:fire(origin, direction)
	
	if fireResult.success then
		-- Play recoil animation
		if systems.animator then
			systems.animator:playRecoilAnimation(fireResult)
		end
		
		-- Apply camera recoil
		if systems.camera then
			systems.camera:applyRecoil(fireResult.recoilVector)
		end
		
		-- Update viewmodel
		if systems.viewmodel then
			systems.viewmodel:onWeaponFired(fireResult)
		end
	end
end

function ClientCore.reloadWeapon(): ()
	if not currentWeapon then
		return
	end
	
	-- Start reload
	local reloadResult = currentWeapon:reload()
	
	if reloadResult.success then
		-- Play reload animation
		if systems.animator then
			systems.animator:playReloadAnimation(reloadResult)
		end
		
		-- Update viewmodel
		if systems.viewmodel then
			systems.viewmodel:onWeaponReloaded(reloadResult)
		end
	end
end

function ClientCore.aimDownSights(aiming: boolean): ()
	if not currentWeapon then
		return
	end
	
	-- Update weapon state
	currentWeapon.state.modes.aimingDownSights = aiming
	
	-- Update camera
	if systems.camera then
		systems.camera:setAiming(aiming)
	end
	
	-- Update viewmodel
	if systems.viewmodel then
		systems.viewmodel:setAiming(aiming)
	end
	
	-- Play ADS animation
	if systems.animator then
		systems.animator:playADSAnimation(aiming)
	end
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function ClientCore.getCurrentWeapon(): any?
	return currentWeapon
end

function ClientCore.getSystem(systemName: string): any?
	return systems[systemName]
end

function ClientCore.isReady(): boolean
	return isInitialized and currentWeapon ~= nil
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function ClientCore.cleanup(): ()
	print("[GSF Client] Cleaning up client systems...")
	
	-- Cleanup systems
	for _, system in pairs(systems) do
		if system and system.cleanup then
			system:cleanup()
		end
	end
	
	-- Clear state
	currentWeapon = nil
	viewmodel = nil
	camera = nil
	isInitialized = false
	
	print("[GSF Client] Client cleanup completed")
end

-- Initialize when script loads
ClientCore.initialize()

return ClientCore
