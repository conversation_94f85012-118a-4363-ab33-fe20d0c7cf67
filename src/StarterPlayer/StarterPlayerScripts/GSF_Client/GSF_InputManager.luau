--!strict
--[[
	Gun System Framework - Input Manager
	
	This module handles all input for weapon controls:
	- Mouse and keyboard input for FPS controls
	- Weapon firing, reloading, and aiming
	- Customizable key bindings
	- Input state management
]]

local UserInputService = game:GetService("UserInputService")
local ContextActionService = game:GetService("ContextActionService")
local Players = game:GetService("Players")

-- ============================================================================
-- INPUT MANAGER
-- ============================================================================

local InputManager = {}
InputManager.__index = InputManager

-- Default key bindings
local DEFAULT_KEYBINDS = {
	fire = Enum.UserInputType.MouseButton1,
	aim = Enum.UserInputType.MouseButton2,
	reload = Enum.KeyCode.R,
	inspect = Enum.KeyCode.T,
	fireMode = Enum.KeyCode.B,
	safety = Enum.KeyCode.V,
	flashlight = Enum.KeyCode.F,
	laser = Enum.KeyCode.L,
}

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function InputManager.new(): InputManager
	local self = setmetatable({}, InputManager)
	
	-- State
	self.keybinds = table.clone(DEFAULT_KEYBINDS)
	self.inputState = {
		firing = false,
		aiming = false,
		reloading = false,
	}
	
	-- Events (using BindableEvents for simplicity)
	self.events = {
		onFirePressed = Instance.new("BindableEvent"),
		onFireReleased = Instance.new("BindableEvent"),
		onAimPressed = Instance.new("BindableEvent"),
		onAimReleased = Instance.new("BindableEvent"),
		onReloadPressed = Instance.new("BindableEvent"),
		onInspectPressed = Instance.new("BindableEvent"),
		onFireModePressed = Instance.new("BindableEvent"),
		onSafetyPressed = Instance.new("BindableEvent"),
		onFlashlightPressed = Instance.new("BindableEvent"),
		onLaserPressed = Instance.new("BindableEvent"),
	}
	
	-- Expose event connections
	self.onFirePressed = self.events.onFirePressed.Event
	self.onFireReleased = self.events.onFireReleased.Event
	self.onAimPressed = self.events.onAimPressed.Event
	self.onAimReleased = self.events.onAimReleased.Event
	self.onReloadPressed = self.events.onReloadPressed.Event
	self.onInspectPressed = self.events.onInspectPressed.Event
	self.onFireModePressed = self.events.onFireModePressed.Event
	self.onSafetyPressed = self.events.onSafetyPressed.Event
	self.onFlashlightPressed = self.events.onFlashlightPressed.Event
	self.onLaserPressed = self.events.onLaserPressed.Event
	
	-- Connections
	self.connections = {}
	
	-- Initialize
	self:_initialize()
	
	return self
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function InputManager:_initialize(): ()
	-- Set up input handling
	self:_setupInputHandling()
	
	-- Set up context actions for mobile support
	self:_setupContextActions()
end

function InputManager:_setupInputHandling(): ()
	-- Handle input began
	self.connections.inputBegan = UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then
			return
		end
		
		self:_handleInputBegan(input)
	end)
	
	-- Handle input ended
	self.connections.inputEnded = UserInputService.InputEnded:Connect(function(input, gameProcessed)
		if gameProcessed then
			return
		end
		
		self:_handleInputEnded(input)
	end)
end

function InputManager:_setupContextActions(): ()
	-- Set up mobile-friendly context actions
	ContextActionService:BindAction(
		"GSF_Fire",
		function(actionName, inputState, inputObject)
			if inputState == Enum.UserInputState.Begin then
				self:_handleInputBegan(inputObject)
			elseif inputState == Enum.UserInputState.End then
				self:_handleInputEnded(inputObject)
			end
		end,
		true, -- Create touch button
		self.keybinds.fire
	)
	
	ContextActionService:BindAction(
		"GSF_Aim",
		function(actionName, inputState, inputObject)
			if inputState == Enum.UserInputState.Begin then
				self:_handleInputBegan(inputObject)
			elseif inputState == Enum.UserInputState.End then
				self:_handleInputEnded(inputObject)
			end
		end,
		true, -- Create touch button
		self.keybinds.aim
	)
	
	ContextActionService:BindAction(
		"GSF_Reload",
		function(actionName, inputState, inputObject)
			if inputState == Enum.UserInputState.Begin then
				self:_handleInputBegan(inputObject)
			end
		end,
		true, -- Create touch button
		self.keybinds.reload
	)
end

-- ============================================================================
-- INPUT HANDLING
-- ============================================================================

function InputManager:_handleInputBegan(input: InputObject): ()
	local inputType = input.UserInputType
	local keyCode = input.KeyCode
	
	-- Fire
	if inputType == self.keybinds.fire or keyCode == self.keybinds.fire then
		if not self.inputState.firing then
			self.inputState.firing = true
			self.events.onFirePressed:Fire()
		end
	end
	
	-- Aim
	if inputType == self.keybinds.aim or keyCode == self.keybinds.aim then
		if not self.inputState.aiming then
			self.inputState.aiming = true
			self.events.onAimPressed:Fire()
		end
	end
	
	-- Reload
	if keyCode == self.keybinds.reload then
		if not self.inputState.reloading then
			self.inputState.reloading = true
			self.events.onReloadPressed:Fire()
		end
	end
	
	-- Inspect
	if keyCode == self.keybinds.inspect then
		self.events.onInspectPressed:Fire()
	end
	
	-- Fire mode
	if keyCode == self.keybinds.fireMode then
		self.events.onFireModePressed:Fire()
	end
	
	-- Safety
	if keyCode == self.keybinds.safety then
		self.events.onSafetyPressed:Fire()
	end
	
	-- Flashlight
	if keyCode == self.keybinds.flashlight then
		self.events.onFlashlightPressed:Fire()
	end
	
	-- Laser
	if keyCode == self.keybinds.laser then
		self.events.onLaserPressed:Fire()
	end
end

function InputManager:_handleInputEnded(input: InputObject): ()
	local inputType = input.UserInputType
	local keyCode = input.KeyCode
	
	-- Fire
	if inputType == self.keybinds.fire or keyCode == self.keybinds.fire then
		if self.inputState.firing then
			self.inputState.firing = false
			self.events.onFireReleased:Fire()
		end
	end
	
	-- Aim
	if inputType == self.keybinds.aim or keyCode == self.keybinds.aim then
		if self.inputState.aiming then
			self.inputState.aiming = false
			self.events.onAimReleased:Fire()
		end
	end
	
	-- Reload (reset state)
	if keyCode == self.keybinds.reload then
		self.inputState.reloading = false
	end
end

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

function InputManager:setKeybind(action: string, input: any): boolean
	if not self.keybinds[action] then
		warn(`[InputManager] Unknown action: {action}`)
		return false
	end
	
	self.keybinds[action] = input
	
	-- Update context actions
	self:_updateContextActions()
	
	return true
end

function InputManager:getKeybind(action: string): any?
	return self.keybinds[action]
end

function InputManager:_updateContextActions(): ()
	-- Unbind existing actions
	ContextActionService:UnbindAction("GSF_Fire")
	ContextActionService:UnbindAction("GSF_Aim")
	ContextActionService:UnbindAction("GSF_Reload")
	
	-- Rebind with new keybinds
	self:_setupContextActions()
end

-- ============================================================================
-- STATE MANAGEMENT
-- ============================================================================

function InputManager:getInputState(action: string): boolean
	return self.inputState[action] or false
end

function InputManager:isFiring(): boolean
	return self.inputState.firing
end

function InputManager:isAiming(): boolean
	return self.inputState.aiming
end

function InputManager:isReloading(): boolean
	return self.inputState.reloading
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function InputManager:enableInput(): ()
	-- Re-enable input handling
	if not self.connections.inputBegan then
		self:_setupInputHandling()
	end
end

function InputManager:disableInput(): ()
	-- Disable input handling
	for _, connection in pairs(self.connections) do
		connection:Disconnect()
	end
	self.connections = {}
	
	-- Reset input state
	self.inputState.firing = false
	self.inputState.aiming = false
	self.inputState.reloading = false
end

function InputManager:resetInputState(): ()
	-- Reset all input states
	self.inputState.firing = false
	self.inputState.aiming = false
	self.inputState.reloading = false
	
	-- Fire release events if needed
	self.events.onFireReleased:Fire()
	self.events.onAimReleased:Fire()
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function InputManager:cleanup(): ()
	-- Disconnect all connections
	for _, connection in pairs(self.connections) do
		connection:Disconnect()
	end
	
	-- Unbind context actions
	ContextActionService:UnbindAction("GSF_Fire")
	ContextActionService:UnbindAction("GSF_Aim")
	ContextActionService:UnbindAction("GSF_Reload")
	
	-- Destroy events
	for _, event in pairs(self.events) do
		event:Destroy()
	end
	
	-- Clear references
	self.keybinds = nil
	self.inputState = nil
	self.events = nil
	self.connections = nil
end

return InputManager
