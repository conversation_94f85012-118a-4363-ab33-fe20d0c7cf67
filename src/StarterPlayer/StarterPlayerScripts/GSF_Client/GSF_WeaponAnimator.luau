--!strict
--[[
	Gun System Framework - Weapon Animator

	This module handles CFrame-based weapon animations:
	- Per-weapon animation systems
	- Recoil, reload, and inspection animations
	- Smooth CFrame interpolation
	- Animation state management
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

-- Import weapon animations
local WeaponAnimations = require(ReplicatedStorage.GSF.Animations.GSF_WeaponAnimations)

-- ============================================================================
-- WEAPON ANIMATOR
-- ============================================================================

local WeaponAnimator = {}
WeaponAnimator.__index = WeaponAnimator

-- Animation configurations
local ANIMATION_CONFIG = {
  -- Recoil settings
  recoil = {
    intensity = 1.0,
    duration = 0.1,
    recovery = 0.3,
    randomness = 0.2,
  },

  -- Reload settings
  reload = {
    duration = 2.0,
    smoothness = 0.8,
  },

  -- ADS settings
  ads = {
    duration = 0.3,
    smoothness = 0.9,
  },

  -- Inspection settings
  inspection = {
    duration = 3.0,
    smoothness = 0.7,
  },
}

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function WeaponAnimator.new(): WeaponAnimator
  local self = setmetatable({}, WeaponAnimator)

  -- State
  self.currentWeapon = nil
  self.isAnimating = false
  self.animationQueue = {}

  -- Animation offsets
  self.recoilOffset = CFrame.new()
  self.reloadOffset = CFrame.new()
  self.adsOffset = CFrame.new()
  self.inspectionOffset = CFrame.new()
  self.currentOffset = CFrame.new()

  -- Animation data
  self.activeAnimations = {}
  self.animationCounter = 0

  -- Connections
  self.connections = {}

  -- Initialize
  self:_initialize()

  return self
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function WeaponAnimator:_initialize(): ()
  -- Start animation update loop
  self.connections.heartbeat = RunService.Heartbeat:Connect(function(deltaTime)
    self:_updateAnimations(deltaTime)
  end)
end

-- ============================================================================
-- WEAPON MANAGEMENT
-- ============================================================================

function WeaponAnimator:setWeapon(weapon: any): ()
  self.currentWeapon = weapon

  -- Load weapon-specific animation settings
  self:_loadWeaponAnimations(weapon)

  -- Reset animation state
  self:_resetAnimationState()
end

function WeaponAnimator:clearWeapon(): ()
  self.currentWeapon = nil

  -- Stop all animations
  self:_stopAllAnimations()

  -- Reset offsets
  self:_resetAnimationState()
end

function WeaponAnimator:_loadWeaponAnimations(weapon: any): ()
  -- Load weapon-specific animation configurations
  -- This would be customized per weapon type
  if weapon.category == "AssaultRifle" then
    ANIMATION_CONFIG.recoil.intensity = 1.2
    ANIMATION_CONFIG.reload.duration = 2.5
  elseif weapon.category == "Pistol" then
    ANIMATION_CONFIG.recoil.intensity = 0.8
    ANIMATION_CONFIG.reload.duration = 1.5
  elseif weapon.category == "SniperRifle" then
    ANIMATION_CONFIG.recoil.intensity = 2.0
    ANIMATION_CONFIG.reload.duration = 3.0
  end
end

function WeaponAnimator:_resetAnimationState(): ()
  self.recoilOffset = CFrame.new()
  self.reloadOffset = CFrame.new()
  self.adsOffset = CFrame.new()
  self.inspectionOffset = CFrame.new()
  self.currentOffset = CFrame.new()
  self.isAnimating = false
  self.animationQueue = {}
end

-- ============================================================================
-- ANIMATION PLAYBACK
-- ============================================================================

function WeaponAnimator:playRecoilAnimation(fireResult: any): ()
  if not self.currentWeapon then
    return
  end

  local animationId = self:_generateAnimationId()

  -- Calculate recoil based on weapon and fire result
  local recoilIntensity = ANIMATION_CONFIG.recoil.intensity
  local recoilVector = fireResult.recoilVector or { X = 0, Y = 0.1, Z = 0 }

  -- Add randomness
  local randomX = (math.random() - 0.5) * ANIMATION_CONFIG.recoil.randomness
  local randomY = (math.random() - 0.5) * ANIMATION_CONFIG.recoil.randomness

  -- Create recoil CFrame
  local recoilCFrame = CFrame.new(
    recoilVector.X + randomX,
    recoilVector.Y + randomY,
    recoilVector.Z
  ) * CFrame.Angles(
    math.rad(-recoilVector.Y * 10 * recoilIntensity),
    math.rad(recoilVector.X * 5 * recoilIntensity),
    0
  )

  -- Create animation data
  local animationData = {
    id = animationId,
    type = "recoil",
    startTime = tick(),
    duration = ANIMATION_CONFIG.recoil.duration,
    recovery = ANIMATION_CONFIG.recoil.recovery,
    targetCFrame = recoilCFrame,
    phase = "recoil", -- "recoil" or "recovery"
  }

  self.activeAnimations[animationId] = animationData
end

function WeaponAnimator:playReloadAnimation(reloadResult: any): ()
  if not self.currentWeapon then
    return
  end

  local animationId = self:_generateAnimationId()

  -- Create reload animation sequence
  local reloadSequence = self:_createReloadSequence(reloadResult)

  local animationData = {
    id = animationId,
    type = "reload",
    startTime = tick(),
    duration = ANIMATION_CONFIG.reload.duration,
    sequence = reloadSequence,
    currentStep = 1,
  }

  self.activeAnimations[animationId] = animationData
  self.isAnimating = true
end

function WeaponAnimator:playADSAnimation(aiming: boolean): ()
  if not self.currentWeapon then
    return
  end

  local animationId = self:_generateAnimationId()

  -- Create ADS offset
  local targetCFrame = aiming and CFrame.new(0, 0, 0.2) or CFrame.new()

  local animationData = {
    id = animationId,
    type = "ads",
    startTime = tick(),
    duration = ANIMATION_CONFIG.ads.duration,
    targetCFrame = targetCFrame,
    startCFrame = self.adsOffset,
  }

  self.activeAnimations[animationId] = animationData
end

function WeaponAnimator:playInspectionAnimation(): ()
  if not self.currentWeapon then
    return
  end

  local animationId = self:_generateAnimationId()

  -- Create inspection sequence
  local inspectionSequence = self:_createInspectionSequence()

  local animationData = {
    id = animationId,
    type = "inspection",
    startTime = tick(),
    duration = ANIMATION_CONFIG.inspection.duration,
    sequence = inspectionSequence,
    currentStep = 1,
  }

  self.activeAnimations[animationId] = animationData
  self.isAnimating = true
end

-- ============================================================================
-- ANIMATION SEQUENCES
-- ============================================================================

function WeaponAnimator:_createReloadSequence(reloadResult: any): { any }
  -- Create a simple reload animation sequence
  -- This would be customized per weapon type
  return {
    {
      time = 0.3,
      cframe = CFrame.new(0, -0.2, 0) * CFrame.Angles(math.rad(10), 0, 0),
      description = "Lower weapon",
    },
    {
      time = 0.8,
      cframe = CFrame.new(-0.1, -0.3, 0) * CFrame.Angles(math.rad(15), math.rad(-10), 0),
      description = "Magazine out",
    },
    {
      time = 1.2,
      cframe = CFrame.new(0.1, -0.3, 0) * CFrame.Angles(math.rad(15), math.rad(10), 0),
      description = "New magazine",
    },
    {
      time = 0.7,
      cframe = CFrame.new(0, 0, 0),
      description = "Return to ready",
    },
  }
end

function WeaponAnimator:_createInspectionSequence(): { any }
  -- Create weapon inspection animation
  return {
    {
      time = 0.5,
      cframe = CFrame.new(0, 0, 0.2) * CFrame.Angles(0, math.rad(15), 0),
      description = "Turn right",
    },
    {
      time = 1.0,
      cframe = CFrame.new(0, 0, 0.2) * CFrame.Angles(0, math.rad(-15), 0),
      description = "Turn left",
    },
    {
      time = 1.0,
      cframe = CFrame.new(0, 0.1, 0.3) * CFrame.Angles(math.rad(-10), 0, 0),
      description = "Lift up",
    },
    {
      time = 0.5,
      cframe = CFrame.new(0, 0, 0),
      description = "Return to ready",
    },
  }
end

-- ============================================================================
-- ANIMATION UPDATE
-- ============================================================================

function WeaponAnimator:_updateAnimations(deltaTime: number): ()
  local currentTime = tick()

  -- Update all active animations
  for animationId, animationData in pairs(self.activeAnimations) do
    if animationData.type == "recoil" then
      self:_updateRecoilAnimation(animationData, currentTime)
    elseif animationData.type == "reload" then
      self:_updateSequenceAnimation(animationData, currentTime, "reload")
    elseif animationData.type == "ads" then
      self:_updateSimpleAnimation(animationData, currentTime, "ads")
    elseif animationData.type == "inspection" then
      self:_updateSequenceAnimation(animationData, currentTime, "inspection")
    end

    -- Remove completed animations
    if self:_isAnimationComplete(animationData, currentTime) then
      self.activeAnimations[animationId] = nil
    end
  end

  -- Combine all animation offsets
  self:_combineAnimationOffsets()
end

function WeaponAnimator:_updateRecoilAnimation(animationData: any, currentTime: number): ()
  local elapsed = currentTime - animationData.startTime

  if animationData.phase == "recoil" then
    if elapsed < animationData.duration then
      -- Recoil phase
      local progress = elapsed / animationData.duration
      local easedProgress = 1 - math.pow(1 - progress, 4) -- Ease out quart
      self.recoilOffset = CFrame.new():Lerp(animationData.targetCFrame, easedProgress)
    else
      -- Switch to recovery phase
      animationData.phase = "recovery"
      animationData.startTime = currentTime
    end
  elseif animationData.phase == "recovery" then
    if elapsed < animationData.recovery then
      -- Recovery phase
      local progress = elapsed / animationData.recovery
      local easedProgress = 1 - math.pow(1 - progress, 4) -- Ease out quart
      self.recoilOffset = animationData.targetCFrame:Lerp(CFrame.new(), easedProgress)
    else
      -- Animation complete
      self.recoilOffset = CFrame.new()
    end
  end
end

function WeaponAnimator:_updateSimpleAnimation(
  animationData: any,
  currentTime: number,
  offsetType: string
): ()
  local elapsed = currentTime - animationData.startTime
  local progress = math.min(elapsed / animationData.duration, 1)

  -- Apply easing
  local easedProgress = 1 - math.pow(1 - progress, 2) -- Ease out quad

  -- Update offset
  if offsetType == "ads" then
    self.adsOffset = animationData.startCFrame:Lerp(animationData.targetCFrame, easedProgress)
  end
end

function WeaponAnimator:_updateSequenceAnimation(
  animationData: any,
  currentTime: number,
  offsetType: string
): ()
  -- This would implement sequence-based animations for reload and inspection
  -- For now, just a simple placeholder
  local elapsed = currentTime - animationData.startTime
  local progress = math.min(elapsed / animationData.duration, 1)

  if offsetType == "reload" then
    -- Simple reload animation
    local reloadProgress = math.sin(progress * math.pi)
    self.reloadOffset = CFrame.new(0, -0.2 * reloadProgress, 0)
  elseif offsetType == "inspection" then
    -- Simple inspection animation
    local inspectionProgress = math.sin(progress * math.pi * 2)
    self.inspectionOffset = CFrame.new(0, 0, 0.1 * inspectionProgress)
      * CFrame.Angles(0, math.rad(15 * inspectionProgress), 0)
  end
end

function WeaponAnimator:_combineAnimationOffsets(): ()
  -- Combine all animation offsets
  self.currentOffset = self.recoilOffset
    * self.reloadOffset
    * self.adsOffset
    * self.inspectionOffset
end

function WeaponAnimator:_isAnimationComplete(animationData: any, currentTime: number): boolean
  local elapsed = currentTime - animationData.startTime

  if animationData.type == "recoil" then
    return animationData.phase == "recovery" and elapsed > animationData.recovery
  else
    return elapsed > animationData.duration
  end
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function WeaponAnimator:_generateAnimationId(): string
  self.animationCounter += 1
  return `animation_{self.animationCounter}_{tick()}`
end

function WeaponAnimator:_stopAllAnimations(): ()
  self.activeAnimations = {}
  self:_resetAnimationState()
end

function WeaponAnimator:getCurrentOffset(): CFrame
  return self.currentOffset
end

function WeaponAnimator:isPlaying(animationType: string?): boolean
  if not animationType then
    return next(self.activeAnimations) ~= nil
  end

  for _, animationData in pairs(self.activeAnimations) do
    if animationData.type == animationType then
      return true
    end
  end

  return false
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function WeaponAnimator:cleanup(): ()
  -- Disconnect all connections
  for _, connection in pairs(self.connections) do
    connection:Disconnect()
  end

  -- Clear state
  self:_stopAllAnimations()
  self.currentWeapon = nil
  self.connections = nil
end

return WeaponAnimator
