--!strict
--[[
	GSF Test Runner
	
	This script runs the GSF validation tests to ensure everything works correctly
	after refactoring.
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Wait for GSF to be available
local GSF = ReplicatedStorage:WaitForChild("GSF")
local ValidationTest = require(GSF.Tests.GSF_ValidationTest)

-- Run tests after a short delay to ensure everything is loaded
task.wait(2)

print("🔬 Running GSF Validation Tests...")
local success = ValidationTest.runAllTests()

if success then
	print("✅ All GSF systems are working correctly!")
else
	warn("❌ Some GSF tests failed. Check the output above for details.")
end
