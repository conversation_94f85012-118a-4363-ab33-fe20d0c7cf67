--!strict
--[[
	Gun System Framework - Hit Registration
	
	This module handles authoritative hit registration on the server:
	- Server-side raycasting and hit detection
	- Damage calculation and application
	- Hit validation and anti-cheat
	- Penetration and ricochet mechanics
]]

local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")
local RunService = game:GetService("RunService")

-- ============================================================================
-- HIT REGISTRATION SYSTEM
-- ============================================================================

local HitRegistration = {}
HitRegistration.__index = HitRegistration

-- Hit registration configuration
local HIT_CONFIG = {
	-- Raycasting settings
	maxRange = 2000,
	raycastParams = {
		FilterType = Enum.RaycastFilterType.Blacklist,
		FilterDescendantsInstances = {},
	},
	
	-- Damage settings
	baseDamage = {
		head = 100,
		torso = 50,
		limbs = 25,
	},
	
	-- Penetration settings
	enablePenetration = true,
	maxPenetrationDepth = 3,
	
	-- Validation settings
	maxTimeDifference = 0.5, -- seconds
	maxPositionDeviation = 5, -- studs
}

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function HitRegistration.new(config: { [string]: any }): HitRegistration
	local self = setmetatable({}, HitRegistration)
	
	-- Configuration
	self.config = config or {}
	
	-- Hit tracking
	self.recentHits = {} -- For validation and anti-cheat
	self.hitCounter = 0
	
	-- Performance tracking
	self.performanceStats = {
		hitsProcessed = 0,
		averageProcessingTime = 0,
		lastProcessingTime = 0,
	}
	
	-- Initialize
	self:_initialize()
	
	return self
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function HitRegistration:_initialize(): ()
	-- Set up raycast parameters
	self:_setupRaycastParams()
	
	-- Start cleanup loop for old hits
	self:_startCleanupLoop()
	
	print("[HitRegistration] Hit registration system initialized")
end

function HitRegistration:_setupRaycastParams(): ()
	-- Create raycast params
	self.raycastParams = RaycastParams.new()
	self.raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
	self.raycastParams.FilterDescendantsInstances = {}
	
	-- Add all player characters to filter (they'll be added dynamically)
	for _, player in ipairs(Players:GetPlayers()) do
		if player.Character then
			table.insert(self.raycastParams.FilterDescendantsInstances, player.Character)
		end
	end
	
	-- Update filter when players spawn
	Players.PlayerAdded:Connect(function(player)
		player.CharacterAdded:Connect(function(character)
			table.insert(self.raycastParams.FilterDescendantsInstances, character)
		end)
	end)
end

function HitRegistration:_startCleanupLoop(): ()
	-- Clean up old hits every 10 seconds
	RunService.Heartbeat:Connect(function()
		if tick() % 10 < 0.1 then -- Approximately every 10 seconds
			self:_cleanupOldHits()
		end
	end)
end

-- ============================================================================
-- HIT PROCESSING
-- ============================================================================

function HitRegistration:processHit(player: Player, fireResult: any): any
	local startTime = os.clock()
	
	-- Perform server-side raycast
	local hitResult = self:_performServerRaycast(fireResult.origin, fireResult.direction, fireResult.weapon)
	
	if hitResult.hit then
		-- Validate hit
		local validationResult = self:_validateHit(player, hitResult, fireResult)
		
		if validationResult.valid then
			-- Calculate damage
			local damageResult = self:_calculateDamage(hitResult, fireResult.weapon)
			
			-- Apply damage
			self:_applyDamage(hitResult.target, damageResult)
			
			-- Record hit for tracking
			self:_recordHit(player, hitResult, damageResult)
			
			-- Update performance stats
			local processingTime = os.clock() - startTime
			self:_updatePerformanceStats(processingTime)
			
			return {
				valid = true,
				hit = hitResult,
				damage = damageResult,
				processingTime = processingTime,
			}
		else
			return {
				valid = false,
				reason = validationResult.reason,
			}
		end
	else
		return {
			valid = true,
			hit = false,
		}
	end
end

function HitRegistration:_performServerRaycast(origin: Vector3, direction: Vector3, weapon: any): any
	-- Calculate max range based on weapon
	local maxRange = weapon.currentStatistics.maxRange or HIT_CONFIG.maxRange
	
	-- Perform raycast
	local raycastResult = Workspace:Raycast(origin, direction * maxRange, self.raycastParams)
	
	if raycastResult then
		-- Determine hit body part
		local bodyPart = self:_determineBodyPart(raycastResult.Instance)
		
		return {
			hit = true,
			position = raycastResult.Position,
			normal = raycastResult.Normal,
			distance = raycastResult.Distance,
			instance = raycastResult.Instance,
			target = self:_getCharacterFromPart(raycastResult.Instance),
			bodyPart = bodyPart,
			material = raycastResult.Material,
		}
	else
		return {
			hit = false,
		}
	end
end

function HitRegistration:_determineBodyPart(hitPart: BasePart): string
	local partName = hitPart.Name:lower()
	
	if partName:find("head") then
		return "head"
	elseif partName:find("torso") or partName:find("chest") then
		return "torso"
	elseif partName:find("arm") or partName:find("leg") then
		return "limbs"
	else
		return "torso" -- Default to torso
	end
end

function HitRegistration:_getCharacterFromPart(part: BasePart): Model?
	local character = part.Parent
	
	-- Check if it's a character
	if character and character:FindFirstChild("Humanoid") then
		return character
	end
	
	-- Check if it's an accessory
	if character and character.Parent and character.Parent:FindFirstChild("Humanoid") then
		return character.Parent
	end
	
	return nil
end

-- ============================================================================
-- HIT VALIDATION
-- ============================================================================

function HitRegistration:_validateHit(player: Player, hitResult: any, fireResult: any): { valid: boolean, reason: string? }
	-- Validate target exists
	if not hitResult.target then
		return { valid = false, reason = "No valid target" }
	end
	
	-- Validate target is not the shooter
	local targetPlayer = Players:GetPlayerFromCharacter(hitResult.target)
	if targetPlayer == player then
		return { valid = false, reason = "Cannot hit self" }
	end
	
	-- Validate hit distance
	if hitResult.distance > HIT_CONFIG.maxRange then
		return { valid = false, reason = "Hit distance too far" }
	end
	
	-- Validate hit timing (basic anti-cheat)
	local currentTime = tick()
	local timeDifference = currentTime - fireResult.timestamp
	
	if timeDifference > HIT_CONFIG.maxTimeDifference then
		return { valid = false, reason = "Hit timing invalid" }
	end
	
	-- Validate player position (anti-teleport check)
	local character = player.Character
	if character and character:FindFirstChild("HumanoidRootPart") then
		local playerPosition = character.HumanoidRootPart.Position
		local expectedOrigin = fireResult.origin
		local positionDeviation = (playerPosition - expectedOrigin).Magnitude
		
		if positionDeviation > HIT_CONFIG.maxPositionDeviation then
			return { valid = false, reason = "Player position deviation too high" }
		end
	end
	
	return { valid = true }
end

function HitRegistration:validateAndProcessHit(player: Player, hitData: any): any
	-- This method validates client-reported hits against server-side calculations
	-- It's used when the client reports a hit for validation
	
	-- Perform our own raycast to verify
	local serverHitResult = self:_performServerRaycast(hitData.origin, hitData.direction, hitData.weapon)
	
	if serverHitResult.hit then
		-- Compare client and server results
		local positionDifference = (serverHitResult.position - hitData.hitPosition).Magnitude
		
		if positionDifference > 2 then -- Allow 2 stud tolerance
			return {
				valid = false,
				reason = "Hit position mismatch",
				serverResult = serverHitResult,
				clientResult = hitData,
			}
		end
		
		-- If validation passes, process the hit
		return self:processHit(player, {
			origin = hitData.origin,
			direction = hitData.direction,
			weapon = hitData.weapon,
			timestamp = hitData.timestamp,
		})
	else
		return {
			valid = false,
			reason = "Server raycast missed",
		}
	end
end

-- ============================================================================
-- DAMAGE CALCULATION
-- ============================================================================

function HitRegistration:_calculateDamage(hitResult: any, weapon: any): any
	-- Base damage from weapon
	local baseDamage = weapon.currentStatistics.damage or 50
	
	-- Body part multiplier
	local bodyPartMultiplier = HIT_CONFIG.baseDamage[hitResult.bodyPart] or HIT_CONFIG.baseDamage.torso
	bodyPartMultiplier = bodyPartMultiplier / HIT_CONFIG.baseDamage.torso -- Normalize
	
	-- Distance falloff
	local distanceMultiplier = self:_calculateDistanceFalloff(hitResult.distance, weapon)
	
	-- Material penetration
	local penetrationMultiplier = self:_calculatePenetrationMultiplier(hitResult.material)
	
	-- Calculate final damage
	local finalDamage = baseDamage * bodyPartMultiplier * distanceMultiplier * penetrationMultiplier
	
	return {
		baseDamage = baseDamage,
		bodyPartMultiplier = bodyPartMultiplier,
		distanceMultiplier = distanceMultiplier,
		penetrationMultiplier = penetrationMultiplier,
		finalDamage = math.floor(finalDamage),
		bodyPart = hitResult.bodyPart,
		distance = hitResult.distance,
	}
end

function HitRegistration:_calculateDistanceFalloff(distance: number, weapon: any): number
	-- Simple linear falloff
	local maxRange = weapon.currentStatistics.maxRange or HIT_CONFIG.maxRange
	local falloffStart = maxRange * 0.3 -- Start falloff at 30% of max range
	
	if distance <= falloffStart then
		return 1.0 -- No falloff
	else
		local falloffDistance = distance - falloffStart
		local falloffRange = maxRange - falloffStart
		local falloffPercent = falloffDistance / falloffRange
		
		return math.max(0.1, 1.0 - falloffPercent * 0.7) -- Minimum 10% damage
	end
end

function HitRegistration:_calculatePenetrationMultiplier(material: Enum.Material): number
	-- Material-based damage reduction
	local materialMultipliers = {
		[Enum.Material.Plastic] = 1.0,
		[Enum.Material.Wood] = 0.9,
		[Enum.Material.Concrete] = 0.7,
		[Enum.Material.Metal] = 0.5,
		[Enum.Material.DiamondPlate] = 0.3,
	}
	
	return materialMultipliers[material] or 1.0
end

-- ============================================================================
-- DAMAGE APPLICATION
-- ============================================================================

function HitRegistration:_applyDamage(target: Model, damageResult: any): ()
	local humanoid = target:FindFirstChild("Humanoid")
	if not humanoid then
		return
	end
	
	-- Apply damage
	humanoid:TakeDamage(damageResult.finalDamage)
	
	-- Create damage indicator (optional)
	self:_createDamageIndicator(target, damageResult)
	
	print(`[HitRegistration] Applied {damageResult.finalDamage} damage to {target.Name} ({damageResult.bodyPart})`)
end

function HitRegistration:_createDamageIndicator(target: Model, damageResult: any): ()
	-- Create a simple damage indicator
	-- This would be more sophisticated in a real implementation
	local head = target:FindFirstChild("Head")
	if not head then
		return
	end
	
	local gui = Instance.new("BillboardGui")
	gui.Size = UDim2.new(2, 0, 1, 0)
	gui.StudsOffset = Vector3.new(0, 2, 0)
	gui.Parent = head
	
	local label = Instance.new("TextLabel")
	label.Size = UDim2.new(1, 0, 1, 0)
	label.BackgroundTransparency = 1
	label.Text = `-{damageResult.finalDamage}`
	label.TextColor3 = Color3.fromRGB(255, 0, 0)
	label.TextScaled = true
	label.Font = Enum.Font.SourceSansBold
	label.Parent = gui
	
	-- Animate and destroy
	game:GetService("Debris"):AddItem(gui, 2)
end

-- ============================================================================
-- HIT TRACKING
-- ============================================================================

function HitRegistration:_recordHit(player: Player, hitResult: any, damageResult: any): ()
	self.hitCounter += 1
	
	local hitRecord = {
		id = self.hitCounter,
		player = player,
		target = hitResult.target,
		position = hitResult.position,
		bodyPart = hitResult.bodyPart,
		damage = damageResult.finalDamage,
		timestamp = tick(),
	}
	
	table.insert(self.recentHits, hitRecord)
	
	-- Update performance stats
	self.performanceStats.hitsProcessed += 1
end

function HitRegistration:_cleanupOldHits(): ()
	local currentTime = tick()
	local cutoffTime = currentTime - 60 -- Keep hits for 1 minute
	
	local newHits = {}
	for _, hit in ipairs(self.recentHits) do
		if hit.timestamp > cutoffTime then
			table.insert(newHits, hit)
		end
	end
	
	self.recentHits = newHits
end

function HitRegistration:_updatePerformanceStats(processingTime: number): ()
	self.performanceStats.lastProcessingTime = processingTime
	self.performanceStats.averageProcessingTime = 
		(self.performanceStats.averageProcessingTime + processingTime) / 2
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function HitRegistration:getRecentHits(player: Player?): { any }
	if player then
		local playerHits = {}
		for _, hit in ipairs(self.recentHits) do
			if hit.player == player then
				table.insert(playerHits, hit)
			end
		end
		return playerHits
	else
		return self.recentHits
	end
end

function HitRegistration:getPerformanceStats(): any
	return self.performanceStats
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function HitRegistration:cleanup(): ()
	-- Clear hit records
	self.recentHits = {}
	
	-- Reset stats
	self.performanceStats = {
		hitsProcessed = 0,
		averageProcessingTime = 0,
		lastProcessingTime = 0,
	}
	
	print("[HitRegistration] Hit registration system cleaned up")
end

return HitRegistration
