--!strict
--[[
	Gun System Framework - Anti-Cheat System
	
	This module provides anti-cheat protection for the weapon system:
	- Fire rate monitoring
	- Position validation
	- Suspicious behavior detection
	- Automatic action taking
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- ============================================================================
-- ANTI-CHEAT SYSTEM
-- ============================================================================

local AntiCheat = {}
AntiCheat.__index = AntiCheat

-- Anti-cheat configuration
local ANTICHEAT_CONFIG = {
	-- Thresholds
	maxSuspicionLevel = 10,
	suspicionDecayRate = 0.1, -- per second
	
	-- Fire rate monitoring
	fireRateWindow = 10, -- seconds
	maxFireRateViolations = 5,
	
	-- Position monitoring
	maxSpeed = 50, -- studs/second
	teleportThreshold = 100, -- studs
	
	-- Actions
	enableAutoKick = true,
	enableAutoTempBan = false,
	logSuspiciousActivity = true,
}

-- Violation types and their suspicion values
local VIOLATION_SUSPICION = {
	fire_rate_exceeded = 2,
	invalid_position = 3,
	teleport_detected = 5,
	invalid_fire = 1,
	invalid_hit = 2,
	impossible_shot = 4,
}

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function AntiCheat.new(config: { [string]: any }): AntiCheat
	local self = setmetatable({}, AntiCheat)
	
	-- Configuration
	self.config = config or {}
	
	-- Player tracking
	self.playerData = {} -- [Player] = { suspicionLevel, violations, etc. }
	
	-- Violation logs
	self.violationLogs = {}
	
	-- Performance tracking
	self.performanceStats = {
		playersMonitored = 0,
		violationsDetected = 0,
		actionsPerformed = 0,
	}
	
	-- Initialize
	self:_initialize()
	
	return self
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function AntiCheat:_initialize(): ()
	-- Set up player monitoring
	self:_setupPlayerMonitoring()
	
	-- Start update loop
	self:_startUpdateLoop()
	
	print("[AntiCheat] Anti-cheat system initialized")
end

function AntiCheat:_setupPlayerMonitoring(): ()
	-- Monitor existing players
	for _, player in ipairs(Players:GetPlayers()) do
		self:_addPlayer(player)
	end
	
	-- Monitor new players
	Players.PlayerAdded:Connect(function(player)
		self:_addPlayer(player)
	end)
	
	-- Clean up when players leave
	Players.PlayerRemoving:Connect(function(player)
		self:_removePlayer(player)
	end)
end

function AntiCheat:_startUpdateLoop(): ()
	-- Update anti-cheat every frame
	RunService.Heartbeat:Connect(function(deltaTime)
		self:update(deltaTime)
	end)
end

-- ============================================================================
-- PLAYER MANAGEMENT
-- ============================================================================

function AntiCheat:_addPlayer(player: Player): ()
	self.playerData[player] = {
		suspicionLevel = 0,
		violations = {},
		fireHistory = {},
		positionHistory = {},
		lastPosition = Vector3.new(),
		lastUpdateTime = tick(),
		isMonitored = true,
	}
	
	self.performanceStats.playersMonitored += 1
	print(`[AntiCheat] Started monitoring player: {player.Name}`)
end

function AntiCheat:_removePlayer(player: Player): ()
	if self.playerData[player] then
		self.playerData[player] = nil
		self.performanceStats.playersMonitored -= 1
		print(`[AntiCheat] Stopped monitoring player: {player.Name}`)
	end
end

function AntiCheat:clearPlayerData(player: Player): ()
	self:_removePlayer(player)
end

-- ============================================================================
-- VIOLATION REPORTING
-- ============================================================================

function AntiCheat:reportSuspiciousActivity(player: Player, violationType: string, details: string?): ()
	local playerData = self.playerData[player]
	if not playerData then
		return
	end
	
	-- Add suspicion
	local suspicionIncrease = VIOLATION_SUSPICION[violationType] or 1
	playerData.suspicionLevel += suspicionIncrease
	
	-- Record violation
	local violation = {
		type = violationType,
		details = details or "",
		timestamp = tick(),
		suspicionAdded = suspicionIncrease,
	}
	
	table.insert(playerData.violations, violation)
	table.insert(self.violationLogs, {
		player = player,
		violation = violation,
	})
	
	-- Update stats
	self.performanceStats.violationsDetected += 1
	
	-- Log if enabled
	if ANTICHEAT_CONFIG.logSuspiciousActivity then
		print(`[AntiCheat] Suspicious activity from {player.Name}: {violationType} - {details or "No details"}`)
	end
	
	-- Check if action is needed
	self:_checkForAction(player, playerData)
end

function AntiCheat:_checkForAction(player: Player, playerData: any): ()
	if playerData.suspicionLevel >= ANTICHEAT_CONFIG.maxSuspicionLevel then
		self:_takeAction(player, playerData)
	end
end

function AntiCheat:_takeAction(player: Player, playerData: any): ()
	print(`[AntiCheat] Taking action against {player.Name} (suspicion level: {playerData.suspicionLevel})`)
	
	-- Log the action
	local actionLog = {
		player = player,
		action = "kick",
		reason = "Suspicious weapon activity",
		timestamp = tick(),
		suspicionLevel = playerData.suspicionLevel,
		violations = #playerData.violations,
	}
	
	table.insert(self.violationLogs, actionLog)
	self.performanceStats.actionsPerformed += 1
	
	-- Take action
	if ANTICHEAT_CONFIG.enableAutoKick then
		player:Kick("Suspicious weapon activity detected. If you believe this is an error, please contact an administrator.")
	elseif ANTICHEAT_CONFIG.enableAutoTempBan then
		-- This would implement temporary banning
		-- For now, just kick
		player:Kick("Temporary ban for suspicious weapon activity.")
	end
end

-- ============================================================================
-- MONITORING SYSTEMS
-- ============================================================================

function AntiCheat:update(deltaTime: number): ()
	local currentTime = tick()
	
	for player, playerData in pairs(self.playerData) do
		if playerData.isMonitored then
			-- Update position monitoring
			self:_updatePositionMonitoring(player, playerData, currentTime)
			
			-- Decay suspicion over time
			self:_decaySuspicion(playerData, deltaTime)
			
			-- Clean up old data
			self:_cleanupOldData(playerData, currentTime)
		end
	end
end

function AntiCheat:_updatePositionMonitoring(player: Player, playerData: any, currentTime: number): ()
	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then
		return
	end
	
	local currentPosition = character.HumanoidRootPart.Position
	local lastPosition = playerData.lastPosition
	local timeDelta = currentTime - playerData.lastUpdateTime
	
	if timeDelta > 0 and lastPosition.Magnitude > 0 then
		-- Calculate movement
		local distance = (currentPosition - lastPosition).Magnitude
		local speed = distance / timeDelta
		
		-- Check for impossible speed
		if speed > ANTICHEAT_CONFIG.maxSpeed then
			self:reportSuspiciousActivity(player, "invalid_position", `Speed: {math.floor(speed)} studs/s`)
		end
		
		-- Check for teleportation
		if distance > ANTICHEAT_CONFIG.teleportThreshold and timeDelta < 1 then
			self:reportSuspiciousActivity(player, "teleport_detected", `Distance: {math.floor(distance)} studs`)
		end
	end
	
	-- Update position history
	table.insert(playerData.positionHistory, {
		position = currentPosition,
		timestamp = currentTime,
	})
	
	-- Update tracking data
	playerData.lastPosition = currentPosition
	playerData.lastUpdateTime = currentTime
end

function AntiCheat:_decaySuspicion(playerData: any, deltaTime: number): ()
	-- Gradually reduce suspicion over time
	local decayAmount = ANTICHEAT_CONFIG.suspicionDecayRate * deltaTime
	playerData.suspicionLevel = math.max(0, playerData.suspicionLevel - decayAmount)
end

function AntiCheat:_cleanupOldData(playerData: any, currentTime: number): ()
	local cutoffTime = currentTime - 60 -- Keep data for 1 minute
	
	-- Clean up position history
	local newPositionHistory = {}
	for _, entry in ipairs(playerData.positionHistory) do
		if entry.timestamp > cutoffTime then
			table.insert(newPositionHistory, entry)
		end
	end
	playerData.positionHistory = newPositionHistory
	
	-- Clean up fire history
	local newFireHistory = {}
	for _, entry in ipairs(playerData.fireHistory) do
		if entry.timestamp > cutoffTime then
			table.insert(newFireHistory, entry)
		end
	end
	playerData.fireHistory = newFireHistory
	
	-- Clean up old violations (keep last 10)
	if #playerData.violations > 10 then
		local newViolations = {}
		for i = #playerData.violations - 9, #playerData.violations do
			table.insert(newViolations, playerData.violations[i])
		end
		playerData.violations = newViolations
	end
end

-- ============================================================================
-- FIRE RATE MONITORING
-- ============================================================================

function AntiCheat:monitorFireRate(player: Player, weaponData: any, timestamp: number): ()
	local playerData = self.playerData[player]
	if not playerData then
		return
	end
	
	-- Add fire event to history
	table.insert(playerData.fireHistory, {
		weapon = weaponData.name,
		timestamp = timestamp,
	})
	
	-- Check fire rate in recent window
	local windowStart = timestamp - ANTICHEAT_CONFIG.fireRateWindow
	local firesInWindow = 0
	
	for _, fireEvent in ipairs(playerData.fireHistory) do
		if fireEvent.timestamp > windowStart then
			firesInWindow += 1
		end
	end
	
	-- Calculate fire rate (shots per minute)
	local fireRate = (firesInWindow / ANTICHEAT_CONFIG.fireRateWindow) * 60
	
	-- Get expected fire rate for weapon
	local expectedFireRate = self:_getExpectedFireRate(weaponData)
	
	-- Check if fire rate is exceeded
	if fireRate > expectedFireRate * 1.2 then -- Allow 20% tolerance
		self:reportSuspiciousActivity(
			player, 
			"fire_rate_exceeded", 
			`Rate: {math.floor(fireRate)} RPM (expected: {expectedFireRate} RPM)`
		)
	end
end

function AntiCheat:_getExpectedFireRate(weaponData: any): number
	-- This would look up the expected fire rate for the weapon
	-- For now, use some default values
	local defaultFireRates = {
		["M4A1"] = 750,
		["AK47"] = 600,
		["AWP"] = 40,
		["Glock17"] = 300,
	}
	
	return defaultFireRates[weaponData.name] or 600 -- Default to 600 RPM
end

-- ============================================================================
-- HIT VALIDATION
-- ============================================================================

function AntiCheat:validateHit(player: Player, hitData: any): boolean
	local playerData = self.playerData[player]
	if not playerData then
		return false
	end
	
	-- Check if hit is physically possible
	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then
		return false
	end
	
	local playerPosition = character.HumanoidRootPart.Position
	local hitOrigin = hitData.origin
	
	-- Check if hit origin is reasonable
	local distanceFromPlayer = (hitOrigin - playerPosition).Magnitude
	if distanceFromPlayer > 20 then -- Allow some tolerance for viewmodel
		self:reportSuspiciousActivity(player, "invalid_hit", `Origin distance: {math.floor(distanceFromPlayer)} studs`)
		return false
	end
	
	-- Check hit distance
	local hitDistance = (hitData.hitPosition - hitOrigin).Magnitude
	if hitDistance > 2000 then -- Max reasonable range
		self:reportSuspiciousActivity(player, "impossible_shot", `Distance: {math.floor(hitDistance)} studs`)
		return false
	end
	
	return true
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function AntiCheat:getPlayerSuspicionLevel(player: Player): number
	local playerData = self.playerData[player]
	return playerData and playerData.suspicionLevel or 0
end

function AntiCheat:getPlayerViolations(player: Player): { any }
	local playerData = self.playerData[player]
	return playerData and playerData.violations or {}
end

function AntiCheat:getViolationLogs(): { any }
	return self.violationLogs
end

function AntiCheat:getPerformanceStats(): any
	return self.performanceStats
end

function AntiCheat:setPlayerMonitoring(player: Player, enabled: boolean): ()
	local playerData = self.playerData[player]
	if playerData then
		playerData.isMonitored = enabled
	end
end

function AntiCheat:resetPlayerSuspicion(player: Player): ()
	local playerData = self.playerData[player]
	if playerData then
		playerData.suspicionLevel = 0
		playerData.violations = {}
		print(`[AntiCheat] Reset suspicion for player: {player.Name}`)
	end
end

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

function AntiCheat:updateConfig(newConfig: { [string]: any }): ()
	for key, value in pairs(newConfig) do
		if ANTICHEAT_CONFIG[key] ~= nil then
			ANTICHEAT_CONFIG[key] = value
		end
	end
end

function AntiCheat:getConfig(): any
	return ANTICHEAT_CONFIG
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function AntiCheat:cleanup(): ()
	-- Clear all player data
	self.playerData = {}
	
	-- Clear violation logs
	self.violationLogs = {}
	
	-- Reset stats
	self.performanceStats = {
		playersMonitored = 0,
		violationsDetected = 0,
		actionsPerformed = 0,
	}
	
	print("[AntiCheat] Anti-cheat system cleaned up")
end

return AntiCheat
