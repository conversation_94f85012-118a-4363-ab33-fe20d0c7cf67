--!strict
--[[
	Gun System Framework - Weapon Validator
	
	This module validates all weapon-related actions on the server:
	- Fire rate validation
	- Ammunition tracking
	- Range and ballistics validation
	- Weapon state consistency
]]

local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")

-- ============================================================================
-- WEAPON VALIDATOR
-- ============================================================================

local WeaponValidator = {}
WeaponValidator.__index = WeaponValidator

-- Validation thresholds
local VALIDATION_LIMITS = {
	maxFireRate = 1200, -- RPM
	maxRange = 2000, -- studs
	maxVelocity = 1000, -- studs/second
	minTimeBetweenShots = 0.05, -- seconds
	maxReloadTime = 10, -- seconds
	minReloadTime = 0.5, -- seconds
}

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function WeaponValidator.new(config: { [string]: any }): WeaponValidator
	local self = setmetatable({}, WeaponValidator)
	
	-- Configuration
	self.config = config or {}
	
	-- Player tracking
	self.playerData = {} -- [Player] = { lastFireTime, fireCount, etc. }
	
	-- Weapon database (for validation)
	self.weaponDatabase = {}
	
	-- Initialize
	self:_initialize()
	
	return self
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function WeaponValidator:_initialize(): ()
	-- Load weapon database
	self:_loadWeaponDatabase()
	
	print("[WeaponValidator] Weapon validator initialized")
end

function WeaponValidator:_loadWeaponDatabase(): ()
	-- Load weapon specifications for validation
	-- This would typically load from a data store or configuration
	self.weaponDatabase = {
		["M4A1"] = {
			fireRate = 750, -- RPM
			maxRange = 800,
			muzzleVelocity = 900,
			magazineCapacity = 30,
			reloadTime = 2.5,
			category = "AssaultRifle",
		},
		["AK47"] = {
			fireRate = 600,
			maxRange = 900,
			muzzleVelocity = 715,
			magazineCapacity = 30,
			reloadTime = 2.8,
			category = "AssaultRifle",
		},
		["AWP"] = {
			fireRate = 40,
			maxRange = 2000,
			muzzleVelocity = 850,
			magazineCapacity = 10,
			reloadTime = 3.5,
			category = "SniperRifle",
		},
		["Glock17"] = {
			fireRate = 300,
			maxRange = 200,
			muzzleVelocity = 375,
			magazineCapacity = 17,
			reloadTime = 1.8,
			category = "Pistol",
		},
	}
end

-- ============================================================================
-- FIRE VALIDATION
-- ============================================================================

function WeaponValidator:validateWeaponFire(player: Player, weaponData: any, origin: Vector3, direction: Vector3, timestamp: number): { valid: boolean, reason: string? }
	-- Initialize player data if needed
	if not self.playerData[player] then
		self.playerData[player] = {
			lastFireTime = 0,
			fireCount = 0,
			lastPosition = Vector3.new(),
			ammunitionCount = {},
		}
	end
	
	local playerData = self.playerData[player]
	
	-- Validate weapon exists
	local weaponSpec = self.weaponDatabase[weaponData.name]
	if not weaponSpec then
		return { valid = false, reason = "Unknown weapon: " .. tostring(weaponData.name) }
	end
	
	-- Validate fire rate
	local timeSinceLastFire = timestamp - playerData.lastFireTime
	local minTimeBetweenShots = 60 / weaponSpec.fireRate -- Convert RPM to seconds
	
	if timeSinceLastFire < minTimeBetweenShots then
		return { valid = false, reason = "Fire rate too high" }
	end
	
	-- Validate player position
	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then
		return { valid = false, reason = "Invalid player character" }
	end
	
	local playerPosition = character.HumanoidRootPart.Position
	local distanceFromPlayer = (origin - playerPosition).Magnitude
	
	if distanceFromPlayer > 10 then -- Allow some tolerance for viewmodel offset
		return { valid = false, reason = "Fire origin too far from player" }
	end
	
	-- Validate direction
	if direction.Magnitude < 0.9 or direction.Magnitude > 1.1 then
		return { valid = false, reason = "Invalid direction vector" }
	end
	
	-- Validate ammunition
	local ammoValidation = self:_validateAmmunition(player, weaponData, weaponSpec)
	if not ammoValidation.valid then
		return ammoValidation
	end
	
	-- Validate range (basic check)
	local maxRange = weaponSpec.maxRange or VALIDATION_LIMITS.maxRange
	-- This would be more complex in a real implementation
	
	-- Update player data
	playerData.lastFireTime = timestamp
	playerData.fireCount += 1
	playerData.lastPosition = playerPosition
	
	-- Consume ammunition
	self:_consumeAmmunition(player, weaponData)
	
	return { valid = true }
end

function WeaponValidator:_validateAmmunition(player: Player, weaponData: any, weaponSpec: any): { valid: boolean, reason: string? }
	local playerData = self.playerData[player]
	
	-- Check if player has ammunition for this weapon
	local weaponId = weaponData.id or weaponData.name
	local currentAmmo = playerData.ammunitionCount[weaponId] or weaponSpec.magazineCapacity
	
	if currentAmmo <= 0 then
		return { valid = false, reason = "No ammunition remaining" }
	end
	
	-- Check magazine capacity
	if weaponData.state and weaponData.state.ammunition then
		local stateAmmo = weaponData.state.ammunition.magazine or 0
		if stateAmmo <= 0 then
			return { valid = false, reason = "Empty magazine" }
		end
		
		-- Validate against known capacity
		if stateAmmo > weaponSpec.magazineCapacity then
			return { valid = false, reason = "Magazine capacity exceeded" }
		end
	end
	
	return { valid = true }
end

function WeaponValidator:_consumeAmmunition(player: Player, weaponData: any): ()
	local playerData = self.playerData[player]
	local weaponId = weaponData.id or weaponData.name
	
	-- Decrease ammunition count
	if playerData.ammunitionCount[weaponId] then
		playerData.ammunitionCount[weaponId] = math.max(0, playerData.ammunitionCount[weaponId] - 1)
	end
end

-- ============================================================================
-- RELOAD VALIDATION
-- ============================================================================

function WeaponValidator:validateWeaponReload(player: Player, weaponData: any, timestamp: number): { valid: boolean, reason: string? }
	-- Initialize player data if needed
	if not self.playerData[player] then
		self.playerData[player] = {
			lastFireTime = 0,
			lastReloadTime = 0,
			fireCount = 0,
			lastPosition = Vector3.new(),
			ammunitionCount = {},
		}
	end
	
	local playerData = self.playerData[player]
	
	-- Validate weapon exists
	local weaponSpec = self.weaponDatabase[weaponData.name]
	if not weaponSpec then
		return { valid = false, reason = "Unknown weapon: " .. tostring(weaponData.name) }
	end
	
	-- Validate reload timing
	local timeSinceLastReload = timestamp - playerData.lastReloadTime
	if timeSinceLastReload < VALIDATION_LIMITS.minReloadTime then
		return { valid = false, reason = "Reload too fast" }
	end
	
	-- Check if reload is needed
	local weaponId = weaponData.id or weaponData.name
	local currentAmmo = playerData.ammunitionCount[weaponId] or 0
	
	if currentAmmo >= weaponSpec.magazineCapacity then
		return { valid = false, reason = "Magazine already full" }
	end
	
	-- Validate player has reserve ammunition
	-- This would check player inventory in a real implementation
	
	-- Update player data
	playerData.lastReloadTime = timestamp
	
	-- Refill ammunition
	playerData.ammunitionCount[weaponId] = weaponSpec.magazineCapacity
	
	return { valid = true }
end

-- ============================================================================
-- EQUIP VALIDATION
-- ============================================================================

function WeaponValidator:validateWeaponEquip(player: Player, weaponData: any): { valid: boolean, reason: string? }
	-- Validate weapon exists
	local weaponSpec = self.weaponDatabase[weaponData.name]
	if not weaponSpec then
		return { valid = false, reason = "Unknown weapon: " .. tostring(weaponData.name) }
	end
	
	-- Validate player permissions
	-- This would check if player owns/can use this weapon
	
	-- Validate weapon configuration
	if weaponData.components then
		for componentType, component in pairs(weaponData.components) do
			local componentValidation = self:_validateComponent(component, weaponSpec)
			if not componentValidation.valid then
				return componentValidation
			end
		end
	end
	
	-- Initialize ammunition for this weapon
	if not self.playerData[player] then
		self.playerData[player] = {
			lastFireTime = 0,
			lastReloadTime = 0,
			fireCount = 0,
			lastPosition = Vector3.new(),
			ammunitionCount = {},
		}
	end
	
	local weaponId = weaponData.id or weaponData.name
	self.playerData[player].ammunitionCount[weaponId] = weaponSpec.magazineCapacity
	
	return { valid = true }
end

function WeaponValidator:_validateComponent(component: any, weaponSpec: any): { valid: boolean, reason: string? }
	-- Validate component compatibility
	-- This would check if the component is compatible with the weapon
	
	-- Validate component configuration
	if component.config then
		-- Check for invalid values
		for key, value in pairs(component.config) do
			if type(value) == "number" then
				if value < 0 or value > 1000 then -- Basic sanity check
					return { valid = false, reason = `Invalid component value: {key} = {value}` }
				end
			end
		end
	end
	
	return { valid = true }
end

-- ============================================================================
-- HIT VALIDATION
-- ============================================================================

function WeaponValidator:validateHitData(player: Player, hitData: any): { valid: boolean, reason: string? }
	-- Validate hit data structure
	if not hitData.origin or not hitData.direction or not hitData.target then
		return { valid = false, reason = "Incomplete hit data" }
	end
	
	-- Validate player position
	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then
		return { valid = false, reason = "Invalid player character" }
	end
	
	local playerPosition = character.HumanoidRootPart.Position
	local hitOrigin = hitData.origin
	
	-- Check if hit origin is reasonable
	local distanceFromPlayer = (hitOrigin - playerPosition).Magnitude
	if distanceFromPlayer > 20 then -- Allow some tolerance
		return { valid = false, reason = "Hit origin too far from player" }
	end
	
	-- Validate hit target exists
	if not hitData.target.Parent then
		return { valid = false, reason = "Hit target no longer exists" }
	end
	
	-- Validate hit distance
	local hitDistance = (hitData.hitPosition - hitOrigin).Magnitude
	if hitDistance > VALIDATION_LIMITS.maxRange then
		return { valid = false, reason = "Hit distance too far" }
	end
	
	-- Validate hit direction
	local expectedDirection = (hitData.hitPosition - hitOrigin).Unit
	local directionDifference = (expectedDirection - hitData.direction).Magnitude
	
	if directionDifference > 0.1 then -- Allow some tolerance
		return { valid = false, reason = "Hit direction inconsistent" }
	end
	
	return { valid = true }
end

-- ============================================================================
-- PLAYER MANAGEMENT
-- ============================================================================

function WeaponValidator:addPlayer(player: Player): ()
	self.playerData[player] = {
		lastFireTime = 0,
		lastReloadTime = 0,
		fireCount = 0,
		lastPosition = Vector3.new(),
		ammunitionCount = {},
	}
end

function WeaponValidator:removePlayer(player: Player): ()
	self.playerData[player] = nil
end

function WeaponValidator:getPlayerData(player: Player): any?
	return self.playerData[player]
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function WeaponValidator:getWeaponSpec(weaponName: string): any?
	return self.weaponDatabase[weaponName]
end

function WeaponValidator:updateWeaponDatabase(weaponName: string, spec: any): ()
	self.weaponDatabase[weaponName] = spec
end

function WeaponValidator:getValidationLimits(): any
	return VALIDATION_LIMITS
end

function WeaponValidator:setValidationLimit(limitName: string, value: any): ()
	if VALIDATION_LIMITS[limitName] ~= nil then
		VALIDATION_LIMITS[limitName] = value
	end
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function WeaponValidator:cleanup(): ()
	-- Clear all player data
	self.playerData = {}
	
	print("[WeaponValidator] Weapon validator cleaned up")
end

return WeaponValidator
