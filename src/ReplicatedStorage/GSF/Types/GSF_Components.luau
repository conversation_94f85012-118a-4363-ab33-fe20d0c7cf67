--!strict
--[[
	Gun System Framework - Component-Specific Type Definitions

	This module contains specialized type definitions for different weapon components.
	Each component type has its own interface extending the base WeaponComponent.
]]

local Core = require(script.Parent.GSF_Core)
local Weapon = require(script.Parent.GSF_Weapon)

-- Export base types
export type WeaponComponent = Weapon.WeaponComponent
export type Weapon = Weapon.Weapon
export type WeaponStatistics = Weapon.WeaponStatistics
export type ComponentConfig = Core.ComponentConfig
export type ComponentStats = Core.ComponentStats
export type AmmunitionType = Core.AmmunitionType
export type MaterialType = Core.MaterialType
export type RiflingType = Core.RiflingType
export type FeedType = Core.FeedType
export type ReticleType = Core.ReticleType
export type Vector3 = Core.Vector3
export type AttachmentPoint = Core.AttachmentPoint

-- ============================================================================
-- BARREL COMPONENT
-- ============================================================================

export type BarrelComponent = WeaponComponent & {
  -- Barrel-specific properties
  length: number, -- inches
  bore: number, -- caliber in mm
  rifling: RiflingType,
  twist: number, -- rifling twist rate (1:X)

  -- Threading and attachments
  threadPitch: string?, -- For suppressors/compensators
  gasSystem: "DI" | "Piston" | "Blowback" | "None"?,

  -- Ballistics modification methods
  modifyMuzzleVelocity: (self: BarrelComponent, baseVelocity: number) -> number,
  modifyAccuracy: (self: BarrelComponent, baseAccuracy: number) -> number,
  calculateMuzzleFlash: (self: BarrelComponent, ammunition: AmmunitionType) -> any, -- MuzzleFlashData
  calculateBarrelWear: (self: BarrelComponent, roundsFired: number) -> number,

  -- Barrel-specific data
  roundCount: number, -- Total rounds fired through barrel
  -- heatLevel: number, -- Removed for simplified system
  wearLevel: number, -- 0.0 to 1.0
}

-- ============================================================================
-- SIGHT COMPONENT
-- ============================================================================

export type SightComponent = WeaponComponent & {
  -- Optical properties
  magnification: number,
  fieldOfView: number, -- degrees
  reticleType: ReticleType,
  eyeRelief: number, -- mm

  -- Adjustment capabilities
  adjustmentRange: {
    elevation: number, -- MOA
    windage: number, -- MOA
  },
  clickValue: number, -- MOA per click

  -- Zero settings
  zeroRange: number, -- meters
  elevationAdjustment: number, -- current adjustment in MOA
  windageAdjustment: number, -- current adjustment in MOA

  -- Sight-specific methods
  modifyAimingAccuracy: (self: SightComponent, baseAccuracy: number, range: number) -> number,
  getReticle: (self: SightComponent, range: number, environmental: any) -> any, -- ReticleData
  calculateAimPoint: (self: SightComponent, target: Vector3, weapon: Weapon) -> Vector3,
  adjustZero: (self: SightComponent, elevationClicks: number, windageClicks: number) -> (),

  -- Sight-specific data
  batteryLevel: number?, -- For electronic sights
  illuminationLevel: number?, -- Reticle brightness
  isNightVision: boolean?,
  isThermal: boolean?,
}

-- ============================================================================
-- MAGAZINE COMPONENT
-- ============================================================================

export type MagazineComponent = WeaponComponent & {
  -- Magazine properties
  capacity: number,
  feedType: FeedType,
  compatibleAmmo: { AmmunitionType },
  currentAmmo: AmmunitionType?,

  -- Current state
  currentCount: number,
  ammunition: { AmmunitionType }, -- Ordered list of loaded rounds

  -- Feed reliability
  feedReliability: number, -- 0.0 to 1.0
  springTension: number, -- Affects feed reliability

  -- Magazine-specific methods
  loadAmmunition: (self: MagazineComponent, ammo: AmmunitionType, count: number) -> any, -- LoadResult
  extractAmmunition: (self: MagazineComponent) -> AmmunitionType?,
  getRemainingCapacity: (self: MagazineComponent) -> number,
  isEmpty: (self: MagazineComponent) -> boolean,
  isFull: (self: MagazineComponent) -> boolean,

  -- Advanced magazine functions
  mixAmmunition: (
    self: MagazineComponent,
    ammoTypes: { AmmunitionType },
    pattern: string
  ) -> boolean,
  getTopRound: (self: MagazineComponent) -> AmmunitionType?,
  calculateFeedProbability: (self: MagazineComponent) -> number,

  -- Magazine-specific data
  insertionCount: number, -- Times magazine has been inserted
  dropCount: number, -- Times magazine has been dropped
  wearLevel: number, -- 0.0 to 1.0
}

-- ============================================================================
-- STOCK COMPONENT
-- ============================================================================

export type StockComponent = WeaponComponent & {
  -- Stock properties
  stockType: "Fixed" | "Collapsible" | "Folding" | "Adjustable",
  lengthOfPull: number, -- mm
  combRise: number, -- mm
  castOff: number, -- mm

  -- Adjustability
  isAdjustable: boolean,
  minLength: number?, -- For adjustable stocks
  maxLength: number?,
  currentPosition: number?, -- Current adjustment position

  -- Recoil management
  recoilPadType: "Rubber" | "Gel" | "Hard" | "None",
  recoilReduction: number, -- Percentage reduction

  -- Stock-specific methods
  modifyRecoil: (self: StockComponent, baseRecoil: Vector3) -> Vector3,
  modifyStability: (self: StockComponent, baseStability: number) -> number,
  adjustLength: (self: StockComponent, newPosition: number) -> boolean,

  -- Stock-specific data
  isDeployed: boolean, -- For folding stocks
  adjustmentWear: number, -- Wear on adjustment mechanism
}

-- ============================================================================
-- GRIP COMPONENT
-- ============================================================================

export type GripComponent = WeaponComponent & {
  -- Grip properties
  gripType: "Pistol" | "Vertical" | "Angled" | "Hand Stop",
  gripAngle: number, -- degrees
  textureType: "Smooth" | "Stippled" | "Rubberized" | "Aggressive",

  -- Ergonomics
  palmSwell: boolean,
  fingerGrooves: boolean,
  thumbRest: boolean,

  -- Grip-specific methods
  modifyHandling: (self: GripComponent, baseHandling: number) -> number,
  modifyControlSpeed: (self: GripComponent, baseSpeed: number) -> number,

  -- Grip-specific data
  wearPattern: string?, -- Areas of wear
  gripTape: boolean?, -- Has grip tape applied
}

-- ============================================================================
-- TRIGGER COMPONENT
-- ============================================================================

export type TriggerComponent = WeaponComponent & {
  -- Trigger properties
  triggerType: "Single Stage" | "Two Stage" | "Binary" | "Match",
  pullWeight: number, -- pounds
  travelDistance: number, -- mm
  resetDistance: number, -- mm

  -- Timing properties
  lockTime: number, -- milliseconds
  resetTime: number, -- milliseconds

  -- Trigger-specific methods
  modifyFireRate: (self: TriggerComponent, baseRate: number) -> number,
  modifyAccuracy: (self: TriggerComponent, baseAccuracy: number) -> number,
  calculateTriggerResponse: (self: TriggerComponent, inputForce: number) -> boolean,

  -- Trigger-specific data
  roundCount: number, -- Rounds fired with this trigger
  springWear: number, -- 0.0 to 1.0
  adjustmentScrews: { [string]: number }?, -- Custom adjustments
}

-- ============================================================================
-- SUPPRESSOR COMPONENT
-- ============================================================================

export type SuppressorComponent = WeaponComponent & {
  -- Suppressor properties
  suppressorType: "Baffle" | "Monocore" | "K-Can" | "Integral",
  dbReduction: number, -- Decibel reduction
  backPressure: number, -- Effect on gas system

  -- Physical properties
  boreSize: number, -- mm
  baffleCount: number,
  material: MaterialType,

  -- Performance effects
  velocityChange: number, -- m/s change (usually negative)
  accuracyEffect: number, -- Accuracy modifier

  -- Suppressor-specific methods
  modifySound: (self: SuppressorComponent, baseSound: any) -> any, -- AudioInstance
  modifyMuzzleFlash: (self: SuppressorComponent, baseFlash: any) -> any, -- MuzzleFlashData
  calculateBackPressure: (self: SuppressorComponent, gasVolume: number) -> number,

  -- Suppressor-specific data
  carbonBuildup: number, -- 0.0 to 1.0
  baffleWear: number, -- 0.0 to 1.0
  temperatureRating: number, -- Maximum safe temperature
}

-- ============================================================================
-- LASER/LIGHT COMPONENT
-- ============================================================================

export type LaserLightComponent = WeaponComponent & {
  -- Device properties
  deviceType: "Laser" | "Light" | "Combo",
  laserColor: "Red" | "Green" | "IR"?,
  lightOutput: number?, -- Lumens

  -- Beam properties
  beamDivergence: number?, -- mrad for lasers
  beamWidth: number?, -- mm at 25m
  maxRange: number, -- meters

  -- Power management
  batteryType: string,
  batteryLife: number, -- hours
  currentBatteryLevel: number, -- 0.0 to 1.0

  -- Control options
  activationModes: { "Momentary" | "Constant" | "Strobe" },
  currentMode: string,

  -- Device-specific methods
  activate: (self: LaserLightComponent, mode: string) -> boolean,
  deactivate: (self: LaserLightComponent) -> (),
  adjustIntensity: (self: LaserLightComponent, intensity: number) -> (),
  calculateBeamPosition: (self: LaserLightComponent, range: number) -> Vector3,

  -- Device-specific data
  isActive: boolean,
  currentIntensity: number, -- 0.0 to 1.0
  zeroed: boolean, -- Is laser zeroed to weapon
  zeroRange: number?, -- Range at which laser is zeroed
}

-- ============================================================================
-- COMPONENT FACTORY TYPES
-- ============================================================================

export type ComponentFactoryFunction<T> = (config: ComponentConfig) -> T

export type ComponentFactory = {
  -- Factory registration
  registerFactory: <T>(componentType: string, factory: ComponentFactoryFunction<T>) -> (),
  unregisterFactory: (componentType: string) -> (),

  -- Component creation
  createComponent: (componentType: string, config: ComponentConfig) -> WeaponComponent,
  createFromBlueprint: (blueprint: any) -> WeaponComponent, -- ComponentBlueprint
  cloneComponent: (original: WeaponComponent, modifications: ComponentConfig?) -> WeaponComponent,

  -- Validation and testing
  validateBlueprint: (blueprint: any) -> any, -- ValidationResult
  testComponent: (component: WeaponComponent, testSuite: any) -> any, -- TestResult

  -- Serialization support
  serializeComponent: (component: WeaponComponent) -> any, -- ComponentData
  deserializeComponent: (data: any) -> WeaponComponent, -- ComponentData
}

return {}
