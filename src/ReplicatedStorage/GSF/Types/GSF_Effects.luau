--!strict
--[[
	Gun System Framework - Effects and Events Type Definitions

	This module contains all visual effects, audio effects, and event system type definitions including:
	- Visual effects (muzzle flash, impacts, shells)
	- Audio effects and spatial audio
	- Event bus and messaging system
	- UI and feedback systems
]]

local Core = require(script.Parent.GSF_Core)

-- Import core types
export type Vector3 = Core.Vector3
export type MaterialType = Core.MaterialType
export type AmmunitionType = Core.AmmunitionType

-- ============================================================================
-- VISUAL EFFECTS TYPES
-- ============================================================================

export type EffectType =
	"MuzzleFlash"
| "ShellEjection"
| "Impact"
| "Penetration"
| "Ricochet"
| "Fragmentation"
| "Smoke"
| "Spark"
| "Debris"
| "Tracer"
| "LaserSight"
| "Flashlight"

export type Effect = {
	-- Core properties
	id: string,
	type: EffectType,
	duration: number,
	intensity: number,
	priority: number, -- For LOD system

	-- Spatial properties
	position: Vector3,
	rotation: Vector3,
	scale: Vector3,

	-- Visual components
	particles: Core.Instance?, -- ParticleEmitter
	light: Core.Instance?, -- PointLight
	decal: Core.Instance?, -- Decal
	beam: Core.Instance?, -- Beam

	-- Animation properties
	startTime: number,
	fadeInTime: number?,
	fadeOutTime: number?,

	-- Lifecycle methods
	play: (self: Effect) -> (),
	stop: (self: Effect) -> (),
	update: (self: Effect, deltaTime: number) -> (),
	cleanup: (self: Effect) -> (),

	-- State tracking
	isPlaying: boolean,
	shouldDestroy: boolean,
}

export type MuzzleFlashData = {
	size: number,
	brightness: number,
	color: Color3,
	duration: number,
	particleCount: number,
	smokeAmount: number,
	lightRange: number,
	suppressorModified: boolean,
}

export type ShellEjectionData = {
	shellType: string,
	ejectionVelocity: Vector3,
	ejectionPosition: Vector3,
	rotationVelocity: Vector3,
	bounceCount: number,
	lifetime: number,
	soundOnImpact: boolean,
}

export type ImpactEffectData = {
	material: MaterialType,
	impactVelocity: number,
	impactAngle: number,
	penetrated: boolean,

	-- Effect properties
	sparkCount: number,
	debrisCount: number,
	decalSize: number,
	soundIntensity: number,

	-- Damage visualization
	craterSize: number?,
	bloodEffect: boolean?,
}

-- ============================================================================
-- AUDIO SYSTEM TYPES
-- ============================================================================

export type AudioType =
	"Fire"
| "Reload"
| "DryFire"
| "Impact"
| "Ricochet"
| "ShellDrop"
| "Mechanical"
| "Ambient"
| "UI"

export type AudioInstance = {
	id: string,
	type: AudioType,
	soundId: string,

	-- Spatial properties
	position: Vector3?,
	maxDistance: number,
	rolloffFactor: number,

	-- Audio properties
	volume: number,
	pitch: number,
	looped: boolean,

	-- Environmental effects
	reverbLevel: number?,
	echoDelay: number?,
	occlusionLevel: number?,

	-- Lifecycle
	play: (self: AudioInstance) -> (),
	stop: (self: AudioInstance) -> (),
	fadeIn: (self: AudioInstance, duration: number) -> (),
	fadeOut: (self: AudioInstance, duration: number) -> (),

	-- State
	isPlaying: boolean,
	startTime: number,
	duration: number?,
}

export type AcousticEnvironment = {
	-- Room properties
	roomSize: Vector3,
	wallMaterial: MaterialType,
	reverbTime: number,
	absorptionCoefficient: number,

	-- Environmental factors
	temperature: number,
	humidity: number,
	windSpeed: number,

	-- Occlusion obstacles
	obstacles: { Core.Instance },
}

export type TinnitusEffect = {
	intensity: number, -- 0.0 to 1.0
	duration: number, -- seconds
	frequency: number, -- Hz
	fadeInTime: number,
	fadeOutTime: number,

	-- Trigger conditions
	triggerDistance: number, -- Distance from loud sound
	triggerIntensity: number, -- Minimum sound intensity
	cumulativeThreshold: number, -- Multiple shots threshold
}

-- ============================================================================
-- EVENT SYSTEM TYPES
-- ============================================================================

export type EventType =
	"WeaponFired"
| "WeaponReloaded"
| "WeaponJammed"
| "WeaponSwitched"
| "ComponentAttached"
| "ComponentDetached"
| "ProjectileHit"
| "PlayerDamaged"
| "EffectCreated"
| "AudioPlayed"

export type GameEvent = {
	type: EventType,
	timestamp: number,
	source: string, -- Source identifier
	data: { [string]: any },
	priority: number,

	-- Network properties
	replicateToClients: boolean,
	replicateToServer: boolean,
	targetPlayers: { Core.Player }?,
}

export type EventHandler = (event: GameEvent) -> ()

export type EventBus = {
	-- Event registration
	subscribe: (eventType: EventType, handler: EventHandler) -> string, -- Returns subscription ID
	unsubscribe: (subscriptionId: string) -> boolean,

	-- Event publishing
	publish: (event: GameEvent) -> (),
	publishLocal: (event: GameEvent) -> (),
	publishToPlayer: (event: GameEvent, player: Core.Player) -> (),
	publishToPlayers: (event: GameEvent, players: { Core.Player }) -> (),

	-- Event filtering
	addFilter: (filter: (event: GameEvent) -> boolean) -> string,
	removeFilter: (filterId: string) -> boolean,

	-- Utility
	getSubscriberCount: (eventType: EventType) -> number,
	clearAllSubscriptions: () -> (),
}

-- ============================================================================
-- UI AND FEEDBACK TYPES
-- ============================================================================

export type UIFeedbackType =
	"HitMarker"
| "DamageIndicator"
| "AmmoCounter"
| "CrosshairUpdate"
| "InspectionUI"
| "ReloadIndicator"
| "JamIndicator"
| "OverheatWarning"

export type HitMarkerData = {
	position: Vector3, -- World position of hit
	damage: number,
	isHeadshot: boolean,
	isKill: boolean,

	-- Visual properties
	color: Color3,
	size: number,
	duration: number,
	fadeTime: number,

	-- Animation
	animationType: "Static" | "Expand" | "Pulse" | "Cross",
}

export type DamageIndicatorData = {
	direction: Vector3, -- Direction damage came from
	intensity: number, -- 0.0 to 1.0
	damageType: "Bullet" | "Explosion" | "Fall" | "Other",

	-- Visual properties
	color: Color3,
	duration: number,
	pulseRate: number?,
}

export type CrosshairData = {
	-- Accuracy representation
	spread: number, -- Current weapon spread
	recoil: Vector3, -- Current recoil offset

	-- State indicators
	canFire: boolean,
	isReloading: boolean,
	isAiming: boolean,

	-- Visual properties
	color: Color3,
	opacity: number,
	size: number,
	style: "Dot" | "Cross" | "Circle" | "Custom",
}

-- ============================================================================
-- EFFECT SYSTEM INTERFACES
-- ============================================================================

export type EffectSystem = {
	-- Muzzle flash system
	createMuzzleFlash: (weaponId: string, barrelPosition: Vector3, data: MuzzleFlashData) -> Effect,
	configureMuzzleFlash: (flash: Effect, ammunition: AmmunitionType) -> (),

	-- Shell ejection system
	ejectShell: (weaponId: string, ejectionData: ShellEjectionData) -> Effect,
	calculateEjectionPattern: (weaponType: string, fireMode: string) -> ShellEjectionData,

	-- Impact effects
	createImpactEffect: (position: Vector3, data: ImpactEffectData) -> Effect,
	createPenetrationEffect: (entry: Vector3, exit: Vector3, material: MaterialType) -> Effect,
	createRicochetEffect: (position: Vector3, direction: Vector3, intensity: number) -> Effect,

	-- Environmental effects
	createFragmentationEffect: (position: Vector3, fragments: any) -> Effect, -- fragments from Ballistics
	createSmokeEffect: (position: Vector3, intensity: number, duration: number) -> Effect,

	-- Effect management
	updateEffects: (deltaTime: number) -> (),
	cleanupExpiredEffects: () -> number,
	setEffectLOD: (distance: number, quality: "High" | "Medium" | "Low") -> (),

	-- Performance monitoring
	getActiveEffectCount: () -> number,
	getEffectMemoryUsage: () -> number,
}

export type AudioSystem = {
	-- Weapon sounds
	playFireSound: (
		weaponId: string,
		position: Vector3,
		environment: AcousticEnvironment
	) -> AudioInstance,
	playReloadSound: (weaponId: string, reloadStage: string, position: Vector3) -> AudioInstance,
	playDryFireSound: (weaponId: string, position: Vector3) -> AudioInstance,

	-- Environmental audio
	calculateAcousticShadow: (
		source: Vector3,
		listener: Vector3,
		obstacles: { Core.Instance }
	) -> number,
	applyDistanceAttenuation: (audio: AudioInstance, distance: number) -> (),
	simulateEchoEffect: (audio: AudioInstance, environment: AcousticEnvironment) -> (),

	-- Special effects
	triggerTinnitus: (player: Core.Player, effect: TinnitusEffect) -> (),
	applySuppressorEffect: (audio: AudioInstance, suppressorLevel: number) -> (),
	simulateIndoorReverb: (audio: AudioInstance, room: AcousticEnvironment) -> (),

	-- Audio management
	updateAudio: (deltaTime: number) -> (),
	setAudioLOD: (distance: number, quality: "High" | "Medium" | "Low") -> (),
	getActiveAudioCount: () -> number,
}

return {}
