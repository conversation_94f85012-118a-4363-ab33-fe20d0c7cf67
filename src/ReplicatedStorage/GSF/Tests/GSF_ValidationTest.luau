--!strict
--[[
	Gun System Framework - Validation Test
	
	This test validates that the core GSF systems work correctly after refactoring.
	It tests:
	- Core module initialization
	- Component registry functionality
	- Basic weapon creation
	- Animation system functionality
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import GSF Core
local GSF = require(ReplicatedStorage:WaitForChild("GSF").Core)

-- ============================================================================
-- TEST FRAMEWORK
-- ============================================================================

local ValidationTest = {}

local testResults = {
	passed = 0,
	failed = 0,
	total = 0,
	errors = {}
}

local function assert_test(condition: boolean, testName: string, errorMessage: string?): ()
	testResults.total += 1
	
	if condition then
		testResults.passed += 1
		print(`✅ PASS: {testName}`)
	else
		testResults.failed += 1
		local error = errorMessage or "Test failed"
		table.insert(testResults.errors, `{testName}: {error}`)
		print(`❌ FAIL: {testName} - {error}`)
	end
end

-- ============================================================================
-- CORE TESTS
-- ============================================================================

function ValidationTest.testCoreInitialization(): ()
	print("\n🔧 Testing Core Initialization...")
	
	-- Test that GSF module loads
	assert_test(GSF ~= nil, "GSF module loads", "GSF module is nil")
	assert_test(type(GSF) == "table", "GSF is table", "GSF is not a table")
	
	-- Test version information
	assert_test(GSF.VERSION ~= nil, "Version exists", "VERSION is nil")
	assert_test(type(GSF.VERSION) == "string", "Version is string", "VERSION is not a string")
	
	-- Test core systems exist
	assert_test(GSF.ComponentRegistry ~= nil, "ComponentRegistry exists", "ComponentRegistry is nil")
	assert_test(GSF.WeaponEntity ~= nil, "WeaponEntity exists", "WeaponEntity is nil")
	assert_test(GSF.AnimationSystem ~= nil, "AnimationSystem exists", "AnimationSystem is nil")
	assert_test(GSF.PerformanceMonitor ~= nil, "PerformanceMonitor exists", "PerformanceMonitor is nil")
	
	-- Test initialization
	local success = pcall(function()
		GSF.initialize()
	end)
	assert_test(success, "GSF initializes without error", "GSF initialization threw error")
	assert_test(GSF.isInitialized == true, "GSF initialization flag set", "isInitialized is false")
end

function ValidationTest.testComponentRegistry(): ()
	print("\n🔧 Testing Component Registry...")
	
	-- Test registry exists and has methods
	local registry = GSF.ComponentRegistry
	assert_test(registry.registerFactory ~= nil, "registerFactory method exists", "registerFactory is nil")
	assert_test(registry.createComponent ~= nil, "createComponent method exists", "createComponent is nil")
	assert_test(registry.validateConfig ~= nil, "validateConfig method exists", "validateConfig is nil")
	
	-- Test factory registration
	local testFactory = function(config)
		return {
			id = config.id or "test_component",
			type = "TestComponent",
			name = config.name or "Test Component",
			initialize = function() return { success = true } end,
			dependencies = {},
			conflicts = {},
		}
	end
	
	local success = pcall(function()
		registry.registerFactory("TestComponent", testFactory)
	end)
	assert_test(success, "Factory registration works", "Factory registration failed")
end

function ValidationTest.testAnimationSystem(): ()
	print("\n🔧 Testing Animation System...")
	
	local animSystem = GSF.AnimationSystem
	assert_test(animSystem ~= nil, "AnimationSystem exists", "AnimationSystem is nil")
	assert_test(animSystem.new ~= nil, "AnimationSystem.new exists", "new method is nil")
	
	-- Test creating animation system instance
	local success, animator = pcall(function()
		return animSystem.new({
			model = nil, -- We don't have a model for testing
			settings = animSystem.getDefaultSettings()
		})
	end)
	
	assert_test(success, "Animation system creates instance", "Failed to create animator instance")
	
	if success and animator then
		assert_test(animator.playRecoilAnimation ~= nil, "Recoil animation method exists", "playRecoilAnimation is nil")
		assert_test(animator.updateWeaponSway ~= nil, "Weapon sway method exists", "updateWeaponSway is nil")
	end
end

function ValidationTest.testPerformanceMonitor(): ()
	print("\n🔧 Testing Performance Monitor...")
	
	local perfMon = GSF.PerformanceMonitor
	assert_test(perfMon ~= nil, "PerformanceMonitor exists", "PerformanceMonitor is nil")
	assert_test(perfMon.getPerformanceReport ~= nil, "getPerformanceReport exists", "getPerformanceReport is nil")
	
	-- Test getting performance report
	local success, report = pcall(function()
		return perfMon.getPerformanceReport()
	end)
	
	assert_test(success, "Performance report generates", "Failed to generate performance report")
	
	if success and report then
		assert_test(report.frameRate ~= nil, "Report has frameRate", "frameRate is nil")
		assert_test(report.memory ~= nil, "Report has memory", "memory is nil")
		assert_test(report.timestamp ~= nil, "Report has timestamp", "timestamp is nil")
	end
end

-- ============================================================================
-- MAIN TEST RUNNER
-- ============================================================================

function ValidationTest.runAllTests(): ()
	print("🚀 Starting GSF Validation Tests...")
	print("=" .. string.rep("=", 50))
	
	-- Reset test results
	testResults = {
		passed = 0,
		failed = 0,
		total = 0,
		errors = {}
	}
	
	-- Run all tests
	ValidationTest.testCoreInitialization()
	ValidationTest.testComponentRegistry()
	ValidationTest.testAnimationSystem()
	ValidationTest.testPerformanceMonitor()
	
	-- Print results
	print("\n" .. "=" .. string.rep("=", 50))
	print("📊 TEST RESULTS:")
	print(`Total Tests: {testResults.total}`)
	print(`Passed: {testResults.passed}`)
	print(`Failed: {testResults.failed}`)
	
	if testResults.failed > 0 then
		print("\n❌ FAILED TESTS:")
		for _, error in ipairs(testResults.errors) do
			print(`  • {error}`)
		end
	else
		print("\n🎉 ALL TESTS PASSED!")
	end
	
	local successRate = math.floor((testResults.passed / testResults.total) * 100)
	print(`Success Rate: {successRate}%`)
	
	return testResults.failed == 0
end

return ValidationTest
