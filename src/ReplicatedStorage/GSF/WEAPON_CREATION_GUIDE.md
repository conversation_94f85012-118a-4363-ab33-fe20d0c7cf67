# 🔫 GSF Weapon Creation & Animation Guide

This comprehensive guide covers everything you need to know about creating, configuring, and animating weapons in the Gun System Framework.

## 📋 Table of Contents

1. [Weapon Structure](#weapon-structure)
2. [Creating a New Weapon](#creating-a-new-weapon)
3. [Component Configuration](#component-configuration)
4. [Animation System](#animation-system)
5. [Visual Models](#visual-models)
6. [Audio Configuration](#audio-configuration)
7. [Testing & Validation](#testing--validation)

## 🏗️ Weapon Structure

### Core Weapon Properties

Every weapon in GSF requires these essential properties:

```lua
local weaponConfig = {
  -- Basic Identity
  id = "unique_weapon_id",
  name = "Weapon Display Name",
  category = "AssaultRifle", -- AssaultRifle, Pistol, SniperRifle, SMG, LMG, Shotgun

  -- Base Statistics
  baseStatistics = {
    damage = 35,              -- Base damage per shot
    accuracy = 0.85,          -- Accuracy multiplier (0.0-1.0)
    range = 600,              -- Effective range in meters
    fireRate = 650,           -- Rounds per minute
    muzzleVelocity = 850,     -- Bullet velocity in m/s
    recoil = {
      vertical = 0.15,        -- Vertical recoil multiplier
      horizontal = 0.08,      -- Horizontal recoil multiplier
      pattern = "standard"    -- Recoil pattern type
    },
    reliability = 0.95,       -- Jam resistance (0.0-1.0)
    weight = 3.2,            -- Weight in kg
  },

  -- Firing Configuration
  firingModes = {"semi", "auto"}, -- Available firing modes
  cycleRate = 600,                -- Mechanical cycle rate

  -- Ammunition
  ammunition = {
    chamberedRound = nil,     -- Currently chambered round
    compatibleAmmo = {"556x45", "556x45_AP"}, -- Compatible ammo types
  },

  -- Attachment Points (required for component system)
  attachmentPoints = {
    barrel_mount = {
      name = "barrel_mount",
      position = {X = 0, Y = 0, Z = 0.5},
      rotation = {X = 0, Y = 0, Z = 0},
      occupied = false,
      component = nil,
      compatibleTypes = {"Barrel"}
    },
    -- Add more attachment points as needed
  }
}
```

### Required Attachment Points

Every weapon must have these attachment points:

- **barrel_mount**: For barrel components
- **magazine_well**: For magazine components
- **optic_rail**: For sights and scopes (optional but recommended)
- **stock_mount**: For stock components (rifles only)
- **foregrip_rail**: For grips (optional)
- **muzzle_device**: For suppressors/compensators (optional)

## 🔧 Creating a New Weapon

### Step 1: Define the Weapon Configuration

Create a new file in `src/ReplicatedStorage/GSF/Weapons/` (create this folder if it doesn't exist):

```lua
-- src/ReplicatedStorage/GSF/Weapons/M16A4.luau
local GSF = require(script.Parent.Parent.Core)

local M16A4Config = {
  id = "m16a4_rifle",
  name = "M16A4 Assault Rifle",
  category = "AssaultRifle",

  baseStatistics = {
    damage = 40,
    accuracy = 0.88,
    range = 650,
    fireRate = 700,
    muzzleVelocity = 900,
    recoil = {
      vertical = 0.12,
      horizontal = 0.06,
      pattern = "standard"
    },
    reliability = 0.96,
    weight = 3.8,
  },

  firingModes = {"semi", "burst3"},
  cycleRate = 700,

  ammunition = {
    chamberedRound = nil,
    compatibleAmmo = {"556x45", "556x45_AP", "556x45_HP"}
  },

  attachmentPoints = {
    barrel_mount = {
      name = "barrel_mount",
      position = {X = 0, Y = 0, Z = 0.6},
      rotation = {X = 0, Y = 0, Z = 0},
      occupied = false,
      component = nil,
      compatibleTypes = {"Barrel"}
    },
    magazine_well = {
      name = "magazine_well",
      position = {X = 0, Y = -0.3, Z = 0.1},
      rotation = {X = 0, Y = 0, Z = 0},
      occupied = false,
      component = nil,
      compatibleTypes = {"Magazine"}
    },
    optic_rail = {
      name = "optic_rail",
      position = {X = 0, Y = 0.1, Z = 0.2},
      rotation = {X = 0, Y = 0, Z = 0},
      occupied = false,
      component = nil,
      compatibleTypes = {"Sight"}
    },
    stock_mount = {
      name = "stock_mount",
      position = {X = 0, Y = 0, Z = -0.4},
      rotation = {X = 0, Y = 0, Z = 0},
      occupied = false,
      component = nil,
      compatibleTypes = {"Stock"}
    }
  }
}

return M16A4Config
```

### Step 2: Create the Weapon Factory

```lua
-- src/ReplicatedStorage/GSF/Weapons/M16A4Factory.luau
local GSF = require(script.Parent.Parent.Core)
local M16A4Config = require(script.M16A4)

local M16A4Factory = {}

function M16A4Factory.create(): GSF.Types.Weapon
  -- Create the weapon entity
  local weapon = GSF.WeaponEntity.new(M16A4Config)

  -- Attach default components
  local defaultBarrel = GSF.createBarrel({
    id = "m16a4_default_barrel",
    name = "M16A4 20\" Barrel",
    length = 20,
    bore = 5.56,
    rifling = "1:7",
    material = "ChroMoly",
  })

  local defaultMagazine = GSF.createMagazine({
    id = "stanag_30rd",
    name = "STANAG 30-Round Magazine",
    capacity = 30,
    compatibleAmmo = {"556x45", "556x45_AP"},
  })

  -- Attach components
  weapon:attach(defaultBarrel, "barrel_mount")
  weapon:attach(defaultMagazine, "magazine_well")

  return weapon
end

return M16A4Factory
```

### Step 3: Register the Weapon

Add your weapon to the main GSF initialization:

```lua
-- In src/ReplicatedStorage/GSF/Core/init.luau
local M16A4Factory = require(script.Parent.Weapons.M16A4Factory)

-- Add to weapon factories
GSF.weaponFactories = GSF.weaponFactories or {}
GSF.weaponFactories.M16A4 = M16A4Factory

-- Convenience function
function GSF.createM16A4(): Weapon
  return M16A4Factory.create()
end
```

## 🔩 Component Configuration

### Creating Custom Components

Each component type has specific properties:

#### Barrel Component

```lua
local customBarrel = GSF.createBarrel({
  id = "custom_barrel_id",
  name = "Custom Barrel",
  length = 16,              -- Length in inches
  bore = 5.56,             -- Bore diameter in mm
  rifling = "1:7",         -- Twist rate
  material = "ChroMoly",   -- Material type

  -- Performance modifiers
  accuracyModifier = 0.05,  -- +5% accuracy
  rangeModifier = 50,       -- +50m range
  velocityModifier = 1.02,  -- +2% velocity

  -- Physical properties
  weight = 0.8,            -- Weight in kg
  durability = 1000,       -- Round count before degradation
})
```

#### Magazine Component

```lua
local customMagazine = GSF.createMagazine({
  id = "custom_mag_id",
  name = "Custom Magazine",
  capacity = 30,
  compatibleAmmo = {"556x45"},

  -- Performance modifiers
  reloadSpeedModifier = 1.1, -- +10% reload speed

  -- Physical properties
  weight = 0.3,
  material = "Polymer",
})
```

#### Sight Component

```lua
local customSight = GSF.createSight({
  id = "custom_sight_id",
  name = "Custom Red Dot",
  magnification = 1.0,
  reticleType = "Dot",

  -- Performance modifiers
  accuracyModifier = 0.08,   -- +8% accuracy
  acquisitionSpeedModifier = 1.15, -- +15% target acquisition

  -- Electronic properties (for powered sights)
  batteryLife = 1000,       -- Hours of operation
  illuminationLevels = 10,  -- Brightness settings
})
```

## 🎬 CFrame-Based Animation System

### Animation Overview

GSF uses a **CFrame-based procedural animation system** instead of traditional Roblox animations. This provides:

- **Procedural recoil** that responds to weapon statistics
- **Smooth weapon sway** and breathing effects
- **Component-specific animations** (bolt movement, magazine drops)
- **Customizable animation curves** and timing
- **Performance-optimized** interpolation

### Animation Configuration

Configure animations in your weapon config:

```lua
animationSettings = {
  recoil = {
    enabled = true,
    verticalIntensity = 0.1,    -- Recoil strength
    horizontalIntensity = 0.05,  -- Side-to-side recoil
    duration = 0.15,            -- Recoil animation time
    recoveryTime = 0.3,         -- Recovery time
    randomness = 0.2,           -- Recoil variation
  },

  sway = {
    enabled = true,
    intensity = 0.02,           -- Sway amount
    frequency = 1.5,            -- Sway speed
    breathingEffect = true,     -- Add breathing movement
  },

  reload = {
    enabled = true,
    magazineDropTime = 0.8,     -- Magazine drop timing
    magazineInsertTime = 1.5,   -- Magazine insert timing
    boltReleaseTime = 2.2,      -- Bolt release timing
    smoothing = 0.1,            -- Animation smoothness
  },

  inspection = {
    enabled = true,
    rotationAmount = 45,        -- Inspection rotation degrees
    duration = 2.0,             -- Inspection duration
    smoothing = 0.15,           -- Animation smoothness
  },
}
```

### Animation Configuration

```lua
-- In your weapon config
animations = {
  idle = {
    id = "rbxassetid://123456789",
    speed = 1.0,
    looped = true,
    priority = Enum.AnimationPriority.Core
  },

  fire = {
    id = "rbxassetid://123456790",
    speed = 1.2,
    looped = false,
    priority = Enum.AnimationPriority.Action,
    blendTime = 0.1
  },

  reload_empty = {
    id = "rbxassetid://123456791",
    speed = 1.0,
    looped = false,
    priority = Enum.AnimationPriority.Action,
    duration = 3.2, -- seconds
    keyframes = {
      magazine_out = 0.8,
      magazine_in = 2.1,
      bolt_release = 2.8
    }
  }
}
```

### Animation Events

GSF supports animation events for synchronized actions:

```lua
-- Animation event handling
weapon.animations.reload_empty.events = {
  magazine_out = function(weapon)
    -- Play magazine drop sound
    GSF.AudioSystem.playMagazineOut(weapon)
    -- Spawn magazine drop effect
    GSF.EffectsSystem.createMagazineDrop(weapon)
  end,

  magazine_in = function(weapon)
    -- Play magazine insert sound
    GSF.AudioSystem.playMagazineIn(weapon)
  end,

  bolt_release = function(weapon)
    -- Play bolt sound and chamber round
    GSF.AudioSystem.playBoltRelease(weapon)
    weapon:chamberRound()
  end
}
```

## 🎨 Visual Models

### Model Requirements

Weapon models should follow these guidelines:

1. **Scale**: Use realistic proportions (1 stud = 1 foot)
2. **Pivot Point**: Set at the grip/trigger guard
3. **Orientation**: Barrel pointing in +Z direction
4. **Parts Structure**:

   ```zsh
   WeaponModel
   ├── Body (main weapon body)
   ├── Barrel (separate for attachments)
   ├── Magazine (separate for reloads)
   ├── Stock (if applicable)
   └── AttachmentPoints (folder)
       ├── OpticRail (Part)
       ├── MuzzleDevice (Part)
       └── ForegripRail (Part)
   ```

### Attachment Point Setup

Create invisible parts for attachment points:

```lua
-- Example attachment point part
local opticRail = Instance.new("Part")
opticRail.Name = "OpticRail"
opticRail.Size = Vector3.new(0.1, 0.1, 0.3)
opticRail.Transparency = 1
opticRail.CanCollide = false
opticRail.Anchored = false

-- Position relative to weapon body
local weld = Instance.new("WeldConstraint")
weld.Part0 = weaponBody
weld.Part1 = opticRail
weld.Parent = weaponBody
```

### Component Models

Component models should:

- Have proper attachment points
- Include LOD (Level of Detail) versions
- Use appropriate materials and textures
- Follow the same scale as weapons

## 🔊 Audio Configuration

### Sound Structure

Each weapon needs these audio files:

```lua
local weaponSounds = {
  -- Firing Sounds
  fire_unsuppressed = "rbxassetid://123456789",
  fire_suppressed = "rbxassetid://123456790",

  -- Mechanical Sounds
  bolt_forward = "rbxassetid://123456791",
  bolt_back = "rbxassetid://123456792",
  safety_on = "rbxassetid://123456793",
  safety_off = "rbxassetid://123456794",

  -- Reload Sounds
  magazine_out = "rbxassetid://123456795",
  magazine_in = "rbxassetid://123456796",
  bolt_release = "rbxassetid://123456797",

  -- Handling Sounds
  weapon_draw = "rbxassetid://123456798",
  weapon_holster = "rbxassetid://123456799",
}
```

### Audio Properties

Configure audio properties for realism:

```lua
audioConfig = {
  fire_unsuppressed = {
    volume = 1.0,
    pitch = 1.0,
    rollOffMode = Enum.RollOffMode.InverseTapered,
    maxDistance = 1000,
    effects = {
      reverb = true,
      echo = true,
      distortion = false
    }
  },

  fire_suppressed = {
    volume = 0.3,
    pitch = 0.8,
    rollOffMode = Enum.RollOffMode.InverseTapered,
    maxDistance = 200,
    effects = {
      reverb = false,
      echo = false,
      distortion = false
    }
  }
}
```

## ✅ Testing & Validation

### Basic Weapon Test

```lua
-- Test script example
local GSF = require(game.ReplicatedStorage.GSF.Core)

-- Initialize GSF
GSF.initialize()

-- Create your weapon
local weapon = GSF.createM16A4()

-- Basic functionality tests
print("=== WEAPON TESTS ===")
print("Weapon created:", weapon.name)
print("Can fire:", weapon:canFire())
print("Can reload:", weapon:canReload())

-- Test firing
local fireResult = weapon:fire(
  {X = 0, Y = 5, Z = 0},    -- Origin
  {X = 0, Y = 0, Z = -1}    -- Direction
)

print("Fire result:", fireResult.success)
if fireResult.success then
  print("Projectile created:", fireResult.projectile ~= nil)
end

-- Test component attachment
local customSight = GSF.createSight({
  id = "test_sight",
  magnification = 2.0,
  reticleType = "Crosshair"
})

local attachResult = weapon:attach(customSight, "optic_rail")
print("Sight attached:", attachResult.success)

-- Test statistics
local baseStats = weapon.baseStatistics
local effectiveStats = weapon:getEffectiveStatistics()
print("Base accuracy:", baseStats.accuracy)
print("Effective accuracy:", effectiveStats.accuracy)
```

### Performance Testing

Use the built-in performance test:

```lua
local PerformanceTest = require(game.ReplicatedStorage.GSF.Examples.PerformanceTest)

-- Run comprehensive performance test
PerformanceTest.runComprehensivePerformanceTest()

-- Quick performance check
PerformanceTest.quickPerformanceCheck()
```

### Validation Checklist

Before deploying your weapon:

- [ ] All required attachment points are configured
- [ ] Default components are attached and functional
- [ ] Firing mechanics work correctly
- [ ] Reload system functions properly
- [ ] Component attachment/detachment works
- [ ] Statistics calculations are accurate
- [ ] Audio plays correctly
- [ ] Animations are synchronized
- [ ] Performance is acceptable
- [ ] No console errors or warnings

## 🚀 Advanced Features

### Custom Firing Modes

```lua
-- Add custom firing modes
weapon.customFiringModes = {
  burst3 = {
    roundsPerBurst = 3,
    burstDelay = 0.1,
    interBurstDelay = 0.3
  },

  binary = {
    fireOnPress = true,
    fireOnRelease = true
  }
}
```

### Weapon Variants

Create variants of existing weapons:

```lua
-- M16A4 Carbine variant
local M4A1Config = table.clone(M16A4Config)
M4A1Config.id = "m4a1_carbine"
M4A1Config.name = "M4A1 Carbine"
M4A1Config.baseStatistics.range = 500  -- Shorter range
M4A1Config.firingModes = {"semi", "auto"} -- Full auto instead of burst
```

### Integration with Game Systems

```lua
-- Example integration with inventory system
function IntegrateWithInventory(weapon, player)
  -- Save weapon configuration
  local weaponData = weapon:serialize()

  -- Store in player data
  player.weaponInventory = player.weaponInventory or {}
  table.insert(player.weaponInventory, weaponData)

  -- Handle weapon persistence
  weapon.onComponentAttached:Connect(function(component)
    -- Update saved data when components change
    weaponData = weapon:serialize()
  end)
end
```

## 🔧 Centralized Configuration System

### Configuration Overview

GSF includes a powerful configuration system that allows you to enable/disable any feature:

```lua
local GSF = require(game.ReplicatedStorage.GSF.Core)

-- Quick configuration presets
GSF.ConfigurationSystem.setPerformanceMode()    -- Optimized for performance
GSF.ConfigurationSystem.setHighQualityMode()   -- Maximum visual quality
GSF.ConfigurationSystem.setDevelopmentMode()   -- Debug features enabled
GSF.ConfigurationSystem.setMinimalMode()       -- Minimal features only

-- Individual feature control
GSF.ConfigurationSystem.disableFeature("weapon", "enableJamming")      -- Disable jamming
GSF.ConfigurationSystem.disableFeature("weapon", "enableOverheating")  -- Disable overheating
GSF.ConfigurationSystem.enableFeature("animations", "enableRecoilAnimation")  -- Enable recoil

-- Bulk configuration
GSF.ConfigurationSystem.setBulkConfiguration({
  weapon = {
    enableRecoil = true,
    enableSpread = true,
    enableJamming = false,        -- Disabled as requested
    enableOverheating = false,    -- Disabled as requested
  },
  animations = {
    enableCFrameAnimations = true,
    recoilIntensity = 1.2,
    swayIntensity = 0.8,
  },
  effects = {
    effectQuality = "High",
    enableMuzzleFlash = true,
    enableShellEjection = true,
  }
})

-- Print current configuration
GSF.ConfigurationSystem.printConfiguration()
```

### Available Configuration Categories

- **weapon**: Core weapon mechanics (recoil, spread, jamming, etc.)
- **animations**: CFrame-based animation settings
- **ballistics**: Advanced ballistics features
- **effects**: Visual effects quality and features
- **audio**: Audio system settings
- **performance**: Performance optimization settings
- **ui**: User interface features
- **debug**: Development and debugging tools

### Custom Configuration Profiles

Create your own configuration profiles:

```lua
-- Create a custom profile
GSF.ConfigurationSystem.createProfile("MyGameMode", {
  weapon = {
    enableRecoil = false,
    enableSpread = false,
  },
  effects = {
    effectQuality = "Low",
  },
  animations = {
    enableSwayAnimation = false,
  }
})

-- Load the profile
GSF.ConfigurationSystem.loadProfile("MyGameMode")
```

This guide provides everything needed to create professional-quality weapons in the GSF system. Follow these patterns for consistent, high-quality weapon implementations.
