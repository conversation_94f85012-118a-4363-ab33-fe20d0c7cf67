--!strict
--[[
	Gun System Framework - Suppressor Component Factory

	This module implements a factory for creating suppressor components.
	Suppressors reduce sound signature, muzzle flash, and can affect ballistics.
	Includes realistic suppressor mechanics like back pressure and fouling.
]]

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type SuppressorComponent = Types.SuppressorComponent
type ComponentConfig = Types.ComponentConfig
type WeaponStatistics = Types.WeaponStatistics
type Weapon = Types.Weapon
type AttachmentPoint = Types.AttachmentPoint
type ValidationResult = Types.ValidationResult
type InitResult = Types.InitResult
type AttachResult = Types.AttachResult
type DetachResult = Types.DetachResult
type FireData = Types.FireData
type ReloadData = Types.ReloadData
type MaterialType = Types.MaterialType
type AudioInstance = Types.AudioInstance
type MuzzleFlashData = Types.MuzzleFlashData

-- ============================================================================
-- SUPPRESSOR COMPONENT IMPLEMENTATION
-- ============================================================================

local function createSuppressorComponent(config: ComponentConfig): SuppressorComponent
	-- Validate suppressor-specific configuration
	assert(
		config.customProperties and config.customProperties.suppressorType,
		"Suppressor type is required"
	)
	assert(
		config.customProperties and config.customProperties.dbReduction,
		"Decibel reduction is required"
	)

	local suppressor: SuppressorComponent = {
		-- Base component properties
		id = config.id,
		type = "Suppressor",
		name = config.name,
		description = config.description or "Sound suppressor component",
		version = "1.0.0",

		-- Configuration and stats
		config = config,
		stats = {
			accuracyBonus = config.accuracyModifier or 0.02, -- Slight accuracy improvement
			recoilReduction = config.recoilModifier or 0.05, -- Minimal recoil reduction
			rangeIncrease = 0,
			damageMultiplier = 1,
			massAddition = config.mass or 0.6,
			lengthAddition = config.length or 15, -- cm
			durabilityModifier = 1.0,
			jamChanceModifier = config.customProperties.backPressure * 0.01 or 0, -- Back pressure can increase jam chance
			muzzleFlashReduction = config.customProperties.flashReduction or 0.8, -- 80% flash reduction
			soundSuppressionLevel = config.customProperties.dbReduction or 30, -- dB reduction
			stabilizationBonus = 0.05, -- Slight stabilization from added weight
		},

		-- Dependencies and conflicts
		dependencies = {},
		conflicts = { "Suppressor", "Compensator" }, -- Can't have both suppressor and compensator
		requiredAttachmentPoint = "muzzle_device",

		-- Suppressor-specific properties
		suppressorType = config.customProperties.suppressorType,
		dbReduction = config.customProperties.dbReduction,
		backPressure = config.customProperties.backPressure or 1.2, -- Multiplier for gas system pressure

		-- Physical properties
		boreSize = config.customProperties.boreSize or 6.0, -- mm
		baffleCount = config.customProperties.baffleCount or 8,
		material = config.customProperties.material or "Aluminum",

		-- Performance effects
		velocityChange = config.customProperties.velocityChange or -15, -- m/s (usually negative)
		accuracyEffect = config.customProperties.accuracyEffect or 0.02, -- Accuracy modifier

		-- Suppressor state
		carbonBuildup = 0, -- 0.0 to 1.0
		baffleWear = 0, -- 0.0 to 1.0
		temperatureRating = config.customProperties.temperatureRating or 800, -- Celsius
		currentTemperature = 20, -- Current temperature

		-- Custom data
		customData = config.customProperties,

		-- ============================================================================
		-- LIFECYCLE METHODS
		-- ============================================================================

		initialize = function(self: SuppressorComponent, initConfig: ComponentConfig): InitResult
			if self.dbReduction <= 0 then
				return {
					success = false,
					message = "Decibel reduction must be positive",
					warnings = {},
				}
			end

			if self.boreSize <= 0 then
				return {
					success = false,
					message = "Bore size must be positive",
					warnings = {},
				}
			end

			local warnings = {}

			-- Check for unusual configurations
			if self.dbReduction > 40 then
				table.insert(warnings, "Very high sound reduction may indicate unrealistic suppressor")
			end

			if self.backPressure > 2.0 then
				table.insert(warnings, "High back pressure may cause reliability issues")
			end

			if self.boreSize < 5.0 then
				table.insert(warnings, "Small bore size may cause baffle strikes")
			end

			return {
				success = true,
				message = `Suppressor component {self.id} initialized successfully`,
				warnings = warnings,
			}
		end,

		validate = function(self: SuppressorComponent, weapon: Weapon): ValidationResult
			-- Check if weapon has muzzle device mount
			local hasMuzzleMount = false
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.name == "muzzle_device" then
					hasMuzzleMount = true
					break
				end
			end

			if not hasMuzzleMount then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Weapon does not have a muzzle device mount",
					suggestedAction = "Reject",
				}
			end

			-- Check bore size compatibility
			local barrel = weapon:getComponent("Barrel")
			if barrel and barrel.bore and self.boreSize < barrel.bore then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Suppressor bore is too small for weapon caliber",
					suggestedAction = "Reject",
				}
			end

			return {
				isValid = true,
				severity = "Warning",
				description = "Suppressor is compatible with weapon",
				suggestedAction = "Allow",
			}
		end,

		onAttach = function(
			self: SuppressorComponent,
			weapon: Weapon,
			attachmentPoint: AttachmentPoint
		): AttachResult
			attachmentPoint.occupied = true
			attachmentPoint.component = self

			-- Calculate modified stats
			local modifiedStats = self:modifyStatistics(weapon.baseStatistics)

			return {
				success = true,
				attachmentPoint = attachmentPoint.name,
				modifiedStats = modifiedStats,
				conflicts = {},
			}
		end,

		onDetach = function(self: SuppressorComponent, weapon: Weapon): DetachResult
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.component == self then
					attachmentPoint.occupied = false
					attachmentPoint.component = nil
					break
				end
			end

			return {
				success = true,
				restoredStats = weapon.baseStatistics,
				dependentComponents = {},
			}
		end,

		-- ============================================================================
		-- RUNTIME INTERFACE METHODS
		-- ============================================================================

		onFire = function(self: SuppressorComponent, weapon: Weapon, fireData: FireData): ()
			-- Increase carbon buildup and temperature
			self.carbonBuildup = math.min(1.0, self.carbonBuildup + 0.001)
			self.currentTemperature += 50 -- Rapid heating from gas expansion

			-- Check for overheating
			if self.currentTemperature > self.temperatureRating then
				self.baffleWear = math.min(1.0, self.baffleWear + 0.0001)
			end

			-- Carbon buildup affects performance
			if self.carbonBuildup > 0.8 then
				-- Excessive carbon can cause baffle strikes or reduced effectiveness
				weapon.state.condition.jamChance += 0.001
			end
		end,

		onReload = function(self: SuppressorComponent, weapon: Weapon, reloadData: ReloadData): ()
			-- Cooling during reload
			self.currentTemperature = math.max(20, self.currentTemperature - 10)
		end,

		onUpdate = function(self: SuppressorComponent, weapon: Weapon, deltaTime: number): ()
			-- Passive cooling
			local coolingRate = 5 * deltaTime -- 5 degrees per second
			self.currentTemperature = math.max(20, self.currentTemperature - coolingRate)
		end,

		-- ============================================================================
		-- SUPPRESSOR-SPECIFIC METHODS
		-- ============================================================================

		modifyStatistics = function(
			self: SuppressorComponent,
			baseStats: WeaponStatistics
		): WeaponStatistics
			local modifiedStats = table.clone(baseStats)

			-- Velocity change (usually reduction)
			modifiedStats.muzzleVelocity += self.velocityChange

			-- Accuracy effect (usually slight improvement due to reduced muzzle blast)
			modifiedStats.accuracy *= (1 + self.accuracyEffect)

			-- Back pressure can affect reliability
			if self.backPressure > 1.5 then
				modifiedStats.reliability *= (1 - (self.backPressure - 1.5) * 0.1)
			end

			-- Carbon buildup affects performance
			if self.carbonBuildup > 0.5 then
				local carbonPenalty = (self.carbonBuildup - 0.5) * 0.1
				modifiedStats.accuracy *= (1 - carbonPenalty)
				modifiedStats.reliability *= (1 - carbonPenalty)
			end

			-- Baffle wear affects suppression effectiveness
			if self.baffleWear > 0.3 then
				local wearPenalty = (self.baffleWear - 0.3) * 0.2
				-- Reduced sound suppression (would affect audio system)
				modifiedStats.reliability *= (1 - wearPenalty * 0.5)
			end

			return modifiedStats
		end,

		modifySound = function(self: SuppressorComponent, baseSound: AudioInstance): AudioInstance
			local modifiedSound = table.clone(baseSound)

			-- Calculate effective suppression
			local effectiveSuppression = self.dbReduction

			-- Carbon buildup reduces effectiveness
			if self.carbonBuildup > 0.5 then
				effectiveSuppression *= (1 - (self.carbonBuildup - 0.5))
			end

			-- Baffle wear reduces effectiveness
			if self.baffleWear > 0.3 then
				effectiveSuppression *= (1 - (self.baffleWear - 0.3) * 0.5)
			end

			-- Apply suppression (logarithmic scale for decibels)
			local suppressionFactor = math.pow(10, -effectiveSuppression / 20)
			modifiedSound.volume *= suppressionFactor

			-- Change sound character
			modifiedSound.pitch *= 0.9 -- Slightly lower pitch

			return modifiedSound
		end,

		modifyMuzzleFlash = function(
			self: SuppressorComponent,
			baseFlash: MuzzleFlashData
		): MuzzleFlashData
			local modifiedFlash = table.clone(baseFlash)

			-- Calculate flash reduction
			local flashReduction = 0.8 -- 80% reduction

			-- Carbon buildup reduces effectiveness
			if self.carbonBuildup > 0.6 then
				flashReduction *= (1 - (self.carbonBuildup - 0.6) * 0.5)
			end

			-- Apply flash reduction
			modifiedFlash.size *= (1 - flashReduction)
			modifiedFlash.brightness *= (1 - flashReduction)
			modifiedFlash.lightRange *= (1 - flashReduction)

			-- Suppressors create different flash signature
			modifiedFlash.color = Color3.fromRGB(200, 150, 100) -- More orange/yellow
			modifiedFlash.duration *= 0.7 -- Shorter duration

			return modifiedFlash
		end,

		calculateBackPressure = function(self: SuppressorComponent, gasVolume: number): number
			-- Calculate back pressure effect on gas system
			local basePressure = self.backPressure

			-- Carbon buildup increases back pressure
			if self.carbonBuildup > 0.4 then
				basePressure *= (1 + (self.carbonBuildup - 0.4) * 0.5)
			end

			-- Suppressor type affects back pressure
			if self.suppressorType == "Baffle" then
				basePressure *= 1.0 -- Standard
			elseif self.suppressorType == "Monocore" then
				basePressure *= 0.8 -- Lower back pressure
			elseif self.suppressorType == "K-Can" then
				basePressure *= 1.2 -- Higher back pressure
			end

			return gasVolume * basePressure
		end,

		-- Maintenance methods
		clean = function(self: SuppressorComponent): ()
			-- Reset carbon buildup (would require maintenance action)
			self.carbonBuildup = 0
			print(`[Suppressor] {self.id} has been cleaned`)
		end,

		replaceBaffles = function(self: SuppressorComponent): ()
			-- Reset baffle wear (would require maintenance action)
			self.baffleWear = 0
			print(`[Suppressor] {self.id} baffles have been replaced`)
		end,

		getConditionReport = function(self: SuppressorComponent): { [string]: any }
			return {
				carbonBuildup = math.floor(self.carbonBuildup * 100),
				baffleWear = math.floor(self.baffleWear * 100),
				currentTemperature = math.floor(self.currentTemperature),
				temperatureRating = self.temperatureRating,
				effectiveSuppression = self:_calculateEffectiveSuppression(),
				maintenanceRequired = self.carbonBuildup > 0.8 or self.baffleWear > 0.5,
			}
		end,

		_calculateEffectiveSuppression = function(self: SuppressorComponent): number
			local baseSuppression = self.dbReduction

			-- Reduce effectiveness based on wear and fouling
			if self.carbonBuildup > 0.5 then
				baseSuppression *= (1 - (self.carbonBuildup - 0.5))
			end

			if self.baffleWear > 0.3 then
				baseSuppression *= (1 - (self.baffleWear - 0.3) * 0.5)
			end

			return baseSuppression
		end,
	}

	return suppressor
end

-- ============================================================================
-- FACTORY FUNCTION EXPORT
-- ============================================================================

return createSuppressorComponent
