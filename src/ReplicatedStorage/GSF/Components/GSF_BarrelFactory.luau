--!strict
--[[
	Gun System Framework - Barrel Component Factory

	This module implements a factory for creating barrel components.
	It serves as an example of how to implement component factories
	and demonstrates the component creation pattern.
]]

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type BarrelComponent = Types.BarrelComponent
type ComponentConfig = Types.ComponentConfig
type ComponentStats = Types.ComponentStats
type WeaponStatistics = Types.WeaponStatistics
type Weapon = Types.Weapon
type AttachmentPoint = Types.AttachmentPoint
type ValidationResult = Types.ValidationResult
type InitResult = Types.InitResult
type AttachResult = Types.AttachResult
type DetachResult = Types.DetachResult
type FireData = Types.FireData
type ReloadData = Types.ReloadData
type AmmunitionType = Types.AmmunitionType
type MuzzleFlashData = Types.MuzzleFlashData

-- ============================================================================
-- BARREL COMPONENT IMPLEMENTATION
-- ============================================================================

local function createBarrelComponent(config: ComponentConfig): BarrelComponent
	-- Validate barrel-specific configuration
	assert(config.length, "Barrel length is required")
	assert(config.customProperties and config.customProperties.bore, "Barrel bore is required")

	local barrel: BarrelComponent = {
		-- Base component properties
		id = config.id,
		type = "Barrel",
		name = config.name,
		description = config.description or "Standard barrel component",
		version = "1.0.0",

		-- Configuration and stats
		config = config,
		stats = {
			accuracyBonus = config.accuracyModifier or 0,
			recoilReduction = config.recoilModifier or 0,
			rangeIncrease = config.rangeModifier or 0,
			damageMultiplier = config.damageModifier or 1,
			massAddition = config.mass or 1.0,
			lengthAddition = config.length or 16,
			durabilityModifier = 1.0,
			jamChanceModifier = 0,
			muzzleFlashReduction = 0,
			soundSuppressionLevel = 0,
			stabilizationBonus = 0,
		},

		-- Dependencies and conflicts
		dependencies = {}, -- Barrels don't depend on other components
		conflicts = { "Barrel" }, -- Only one barrel per weapon
		requiredAttachmentPoint = "barrel_mount",

		-- Barrel-specific properties
		length = config.length,
		bore = config.customProperties.bore,
		rifling = config.customProperties.rifling or "Standard",
		twist = config.customProperties.twist or 1.0,
		threadPitch = config.customProperties.threadPitch,
		gasSystem = config.customProperties.gasSystem or "DI",

		-- Barrel state
		roundCount = 0,
		heatLevel = 0,
		wearLevel = 0,

		-- Custom data
		customData = config.customProperties,

		-- ============================================================================
		-- LIFECYCLE METHODS
		-- ============================================================================

		initialize = function(self: BarrelComponent, initConfig: ComponentConfig): InitResult
			-- Validate barrel configuration
			if self.length <= 0 then
				return {
					success = false,
					message = "Barrel length must be positive",
					warnings = {},
				}
			end

			if self.bore <= 0 then
				return {
					success = false,
					message = "Barrel bore must be positive",
					warnings = {},
				}
			end

			local warnings = {}

			-- Check for unusual configurations
			if self.length < 10 then
				table.insert(warnings, "Very short barrel may affect ballistics significantly")
			end

			if self.length > 30 then
				table.insert(warnings, "Very long barrel may affect weapon handling")
			end

			return {
				success = true,
				message = `Barrel component {self.id} initialized successfully`,
				warnings = warnings,
			}
		end,

		validate = function(self: BarrelComponent, weapon: Weapon): ValidationResult
			-- Check if weapon can accept this barrel
			local hasBarrelMount = false
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.name == "barrel_mount" then
					hasBarrelMount = true
					break
				end
			end

			if not hasBarrelMount then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Weapon does not have a barrel mount",
					suggestedAction = "Reject",
				}
			end

			return {
				isValid = true,
				severity = "Warning",
				description = "Barrel is compatible with weapon",
				suggestedAction = "Allow",
			}
		end,

		onAttach = function(
			self: BarrelComponent,
			weapon: Weapon,
			attachmentPoint: AttachmentPoint
		): AttachResult
			-- Mark attachment point as occupied
			attachmentPoint.occupied = true
			attachmentPoint.component = self

			-- Calculate modified stats
			local modifiedStats = self:modifyStatistics(weapon.baseStatistics)

			return {
				success = true,
				attachmentPoint = attachmentPoint.name,
				modifiedStats = modifiedStats,
				conflicts = {},
			}
		end,

		onDetach = function(self: BarrelComponent, weapon: Weapon): DetachResult
			-- Find and clear attachment point
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.component == self then
					attachmentPoint.occupied = false
					attachmentPoint.component = nil
					break
				end
			end

			return {
				success = true,
				restoredStats = weapon.baseStatistics,
				dependentComponents = {},
			}
		end,

		-- ============================================================================
		-- RUNTIME INTERFACE METHODS
		-- ============================================================================

		onFire = function(self: BarrelComponent, weapon: Weapon, fireData: FireData): ()
			-- Increase round count and heat
			self.roundCount += 1
			self.heatLevel += 0.1

			-- Calculate wear based on ammunition type and heat
			local wearIncrease = 0.0001 -- Base wear per shot
			if fireData.ammunition == "50BMG" then
				wearIncrease *= 3 -- High-power rounds cause more wear
			end

			if self.heatLevel > 0.8 then
				wearIncrease *= 2 -- Hot barrels wear faster
			end

			self.wearLevel = math.min(1.0, self.wearLevel + wearIncrease)

			-- Cool down over time (simplified)
			self.heatLevel = math.max(0, self.heatLevel - 0.01)
		end,

		onReload = function(self: BarrelComponent, weapon: Weapon, reloadData: ReloadData): ()
			-- Barrel cooling during reload
			self.heatLevel = math.max(0, self.heatLevel - 0.05)
		end,

		onUpdate = function(self: BarrelComponent, weapon: Weapon, deltaTime: number): ()
			-- Passive cooling
			local coolingRate = 0.1 * deltaTime -- Cool 0.1 units per second
			self.heatLevel = math.max(0, self.heatLevel - coolingRate)
		end,

		-- ============================================================================
		-- BARREL-SPECIFIC METHODS
		-- ============================================================================

		modifyStatistics = function(
			self: BarrelComponent,
			baseStats: WeaponStatistics
		): WeaponStatistics
			local modifiedStats = table.clone(baseStats)

			-- Barrel length affects muzzle velocity and accuracy
			local lengthFactor = self.length / 16 -- Normalized to 16" barrel
			modifiedStats.muzzleVelocity *= (1 + (lengthFactor - 1) * 0.02) -- 2% per inch
			modifiedStats.accuracy *= (1 + (lengthFactor - 1) * 0.01) -- 1% per inch

			-- Wear affects accuracy
			modifiedStats.accuracy *= (1 - self.wearLevel * 0.2) -- Up to 20% accuracy loss

			-- Heat affects accuracy
			if self.heatLevel > 0.5 then
				modifiedStats.accuracy *= (1 - (self.heatLevel - 0.5) * 0.1)
			end

			return modifiedStats
		end,

		modifyMuzzleVelocity = function(self: BarrelComponent, baseVelocity: number): number
			local lengthFactor = self.length / 16
			local wearFactor = 1 - (self.wearLevel * 0.05) -- Up to 5% velocity loss
			return baseVelocity * lengthFactor * wearFactor
		end,

		modifyAccuracy = function(self: BarrelComponent, baseAccuracy: number): number
			local lengthFactor = 1 + (self.length - 16) * 0.001
			local wearFactor = 1 - (self.wearLevel * 0.2)
			local heatFactor = self.heatLevel > 0.5 and (1 - (self.heatLevel - 0.5) * 0.1) or 1
			return baseAccuracy * lengthFactor * wearFactor * heatFactor
		end,

		calculateMuzzleFlash = function(
			self: BarrelComponent,
			ammunition: AmmunitionType
		): MuzzleFlashData
			-- Base muzzle flash properties
			local flashData: MuzzleFlashData = {
				size = 1.0,
				brightness = 1.0,
				color = Color3.fromRGB(255, 200, 100),
				duration = 0.1,
				particleCount = 50,
				smokeAmount = 0.3,
				lightRange = 10,
				suppressorModified = false,
			}

			-- Barrel length affects flash size (shorter = bigger flash)
			flashData.size *= (20 / self.length) -- Inverse relationship

			-- Ammunition type affects flash
			if ammunition == "50BMG" then
				flashData.size *= 1.5
				flashData.brightness *= 1.3
				flashData.particleCount *= 1.2
			elseif ammunition == "9x19" then
				flashData.size *= 0.7
				flashData.brightness *= 0.8
			end

			return flashData
		end,

		calculateBarrelWear = function(self: BarrelComponent, roundsFired: number): number
			-- Calculate expected wear based on round count
			local baseWearRate = 0.0001 -- Wear per round
			return math.min(1.0, roundsFired * baseWearRate)
		end,
	}

	return barrel
end

-- ============================================================================
-- FACTORY FUNCTION EXPORT
-- ============================================================================

return createBarrelComponent
