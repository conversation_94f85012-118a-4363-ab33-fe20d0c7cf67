--!strict
--[[
	Gun System Framework - Sight/Scope Component Factory

	This module implements a comprehensive factory for creating sight and scope components.
	Supports various sight types including iron sights, red dots, holographic sights,
	and magnified scopes with realistic ballistics compensation.
]]

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type SightComponent = Types.SightComponent
type ComponentConfig = Types.ComponentConfig
type WeaponStatistics = Types.WeaponStatistics
type Weapon = Types.Weapon
type AttachmentPoint = Types.AttachmentPoint
type ValidationResult = Types.ValidationResult
type InitResult = Types.InitResult
type AttachResult = Types.AttachResult
type DetachResult = Types.DetachResult
type FireData = Types.FireData
type ReloadData = Types.ReloadData
type ReticleType = Types.ReticleType
type Vector3 = Types.Vector3

-- ============================================================================
-- SIGHT COMPONENT IMPLEMENTATION
-- ============================================================================

local function createSightComponent(config: ComponentConfig): SightComponent
	-- Validate sight-specific configuration
	assert(
		config.customProperties and config.customProperties.magnification,
		"Sight magnification is required"
	)
	assert(
		config.customProperties and config.customProperties.reticleType,
		"Reticle type is required"
	)

	local sight: SightComponent = {
		-- Base component properties
		id = config.id,
		type = "Sight",
		name = config.name,
		description = config.description or "Optical sight component",
		version = "1.0.0",

		-- Configuration and stats
		config = config,
		stats = {
			accuracyBonus = config.accuracyModifier or 0,
			recoilReduction = 0,
			rangeIncrease = config.rangeModifier or 0,
			damageMultiplier = 1,
			massAddition = config.mass or 0.3,
			lengthAddition = config.length or 0,
			durabilityModifier = 1.0,
			jamChanceModifier = 0,
			muzzleFlashReduction = 0,
			soundSuppressionLevel = 0,
			stabilizationBonus = config.customProperties.stabilizationBonus or 0,
		},

		-- Dependencies and conflicts
		dependencies = {},
		conflicts = { "Sight", "Scope" }, -- Only one sight per weapon
		requiredAttachmentPoint = "optic_rail",

		-- Sight-specific properties
		magnification = config.customProperties.magnification,
		fieldOfView = config.customProperties.fieldOfView
			or (60 / config.customProperties.magnification),
		reticleType = config.customProperties.reticleType,
		eyeRelief = config.customProperties.eyeRelief or 100, -- mm

		-- Adjustment capabilities
		adjustmentRange = {
			elevation = config.customProperties.elevationRange or 20, -- MOA
			windage = config.customProperties.windageRange or 20, -- MOA
		},
		clickValue = config.customProperties.clickValue or 0.25, -- MOA per click

		-- Zero settings
		zeroRange = config.customProperties.zeroRange or 100, -- meters
		elevationAdjustment = 0, -- current adjustment in MOA
		windageAdjustment = 0, -- current adjustment in MOA

		-- Sight-specific data
		batteryLevel = config.customProperties.batteryLevel or 1.0,
		illuminationLevel = config.customProperties.illuminationLevel or 0.5,
		isNightVision = config.customProperties.isNightVision or false,
		isThermal = config.customProperties.isThermal or false,

		-- Custom data
		customData = config.customProperties,

		-- ============================================================================
		-- LIFECYCLE METHODS
		-- ============================================================================

		initialize = function(self: SightComponent, initConfig: ComponentConfig): InitResult
			if self.magnification <= 0 then
				return {
					success = false,
					message = "Sight magnification must be positive",
					warnings = {},
				}
			end

			if self.fieldOfView <= 0 or self.fieldOfView > 180 then
				return {
					success = false,
					message = "Field of view must be between 0 and 180 degrees",
					warnings = {},
				}
			end

			local warnings = {}

			-- Check for unusual configurations
			if self.magnification > 20 then
				table.insert(warnings, "Very high magnification may affect target acquisition")
			end

			if self.fieldOfView < 5 then
				table.insert(warnings, "Very narrow field of view may limit situational awareness")
			end

			if self.batteryLevel and self.batteryLevel < 0.2 then
				table.insert(warnings, "Low battery level - consider replacement")
			end

			return {
				success = true,
				message = `Sight component {self.id} initialized successfully`,
				warnings = warnings,
			}
		end,

		validate = function(self: SightComponent, weapon: Weapon): ValidationResult
			-- Check if weapon has optic rail
			local hasOpticRail = false
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.name == "optic_rail" then
					hasOpticRail = true
					break
				end
			end

			if not hasOpticRail then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Weapon does not have an optic rail",
					suggestedAction = "Reject",
				}
			end

			-- Check if magnification is appropriate for weapon category
			if weapon.category == "Pistol" and self.magnification > 4 then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Warning",
					description = "High magnification sight may not be suitable for pistol",
					suggestedAction = "Flag",
				}
			end

			return {
				isValid = true,
				severity = "Warning",
				description = "Sight is compatible with weapon",
				suggestedAction = "Allow",
			}
		end,

		onAttach = function(
			self: SightComponent,
			weapon: Weapon,
			attachmentPoint: AttachmentPoint
		): AttachResult
			attachmentPoint.occupied = true
			attachmentPoint.component = self

			-- Calculate modified stats
			local modifiedStats = self:modifyStatistics(weapon.baseStatistics)

			return {
				success = true,
				attachmentPoint = attachmentPoint.name,
				modifiedStats = modifiedStats,
				conflicts = {},
			}
		end,

		onDetach = function(self: SightComponent, weapon: Weapon): DetachResult
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.component == self then
					attachmentPoint.occupied = false
					attachmentPoint.component = nil
					break
				end
			end

			return {
				success = true,
				restoredStats = weapon.baseStatistics,
				dependentComponents = {},
			}
		end,

		-- ============================================================================
		-- RUNTIME INTERFACE METHODS
		-- ============================================================================

		onFire = function(self: SightComponent, weapon: Weapon, fireData: FireData): ()
			-- Reduce battery life for electronic sights
			if self.batteryLevel and self.batteryLevel > 0 then
				self.batteryLevel = math.max(0, self.batteryLevel - 0.0001)
			end
		end,

		onReload = function(self: SightComponent, weapon: Weapon, reloadData: ReloadData): ()
			-- No specific reload behavior for sights
		end,

		onUpdate = function(self: SightComponent, weapon: Weapon, deltaTime: number): ()
			-- Battery drain over time for electronic sights
			if self.batteryLevel and self.batteryLevel > 0 then
				local drainRate = 0.00001 -- Very slow drain when not firing
				self.batteryLevel = math.max(0, self.batteryLevel - drainRate * deltaTime)
			end
		end,

		-- ============================================================================
		-- SIGHT-SPECIFIC METHODS
		-- ============================================================================

		modifyStatistics = function(
			self: SightComponent,
			baseStats: WeaponStatistics
		): WeaponStatistics
			local modifiedStats = table.clone(baseStats)

			-- Magnification improves effective accuracy at range
			local magnificationBonus = math.log(self.magnification) * 0.1 -- Logarithmic scaling
			modifiedStats.accuracy *= (1 + magnificationBonus)

			-- High magnification reduces close-range effectiveness
			if self.magnification > 4 then
				local cqbPenalty = (self.magnification - 4) * 0.02
				modifiedStats.recoil.recovery *= (1 - cqbPenalty)
			end

			-- Electronic sights with low battery have reduced effectiveness
			if self.batteryLevel and self.batteryLevel < 0.1 then
				modifiedStats.accuracy *= 0.8 -- 20% accuracy penalty
			end

			return modifiedStats
		end,

		modifyAimingAccuracy = function(
			self: SightComponent,
			baseAccuracy: number,
			range: number
		): number
			-- Calculate accuracy bonus based on magnification and range
			local optimalRange = self.zeroRange
			local rangeFactor = 1.0

			-- Accuracy is best at zero range, degrades with distance
			if range > optimalRange then
				rangeFactor = optimalRange / range
			end

			-- Magnification helps at longer ranges
			local magnificationBonus = math.min(self.magnification / 4, 2.0) -- Cap at 2x bonus

			return baseAccuracy * rangeFactor * magnificationBonus
		end,

		getReticle = function(self: SightComponent, range: number, environmental: any): any
			-- Return reticle data for UI rendering
			local reticleData = {
				type = self.reticleType,
				magnification = self.magnification,
				illuminated = self.illuminationLevel > 0,
				brightness = self.illuminationLevel,
				batteryLevel = self.batteryLevel,

				-- Ballistic compensation
				elevationOffset = self:_calculateElevationOffset(range),
				windageOffset = self:_calculateWindageOffset(environmental),

				-- Reticle-specific properties
				dotSize = self.reticleType == "Dot" and 2 or nil,
				crosshairThickness = self.reticleType == "Cross" and 1 or nil,
				milDotSpacing = self.reticleType == "Mil-Dot" and 3.6 or nil, -- 3.6 inches at 100 yards
			}

			return reticleData
		end,

		calculateAimPoint = function(self: SightComponent, target: Vector3, weapon: Weapon): Vector3
			-- Calculate where to aim to hit the target
			local weaponPosition = weapon.instance and weapon.instance.Position or { X = 0, Y = 0, Z = 0 }
			local distance = math.sqrt(
				(target.X - weaponPosition.X) ^ 2
					+ (target.Y - weaponPosition.Y) ^ 2
					+ (target.Z - weaponPosition.Z) ^ 2
			)

			-- Apply ballistic compensation
			local elevationOffset = self:_calculateElevationOffset(distance)
			local windageOffset = self:_calculateWindageOffset(nil) -- Would use environmental data

			return {
				X = target.X + windageOffset,
				Y = target.Y + elevationOffset,
				Z = target.Z,
			}
		end,

		adjustZero = function(self: SightComponent, elevationClicks: number, windageClicks: number): ()
			-- Adjust sight zero
			self.elevationAdjustment += elevationClicks * self.clickValue
			self.windageAdjustment += windageClicks * self.clickValue

			-- Clamp to adjustment range
			self.elevationAdjustment = math.max(
				-self.adjustmentRange.elevation,
				math.min(self.adjustmentRange.elevation, self.elevationAdjustment)
			)
			self.windageAdjustment = math.max(
				-self.adjustmentRange.windage,
				math.min(self.adjustmentRange.windage, self.windageAdjustment)
			)
		end,

		-- ============================================================================
		-- PRIVATE HELPER METHODS
		-- ============================================================================

		_calculateElevationOffset = function(self: SightComponent, range: number): number
			-- Simplified bullet drop calculation
			local zeroRange = self.zeroRange
			if range <= zeroRange then
				return 0
			end

			-- Basic ballistic arc calculation
			local dropFactor = ((range - zeroRange) / 100) ^ 2 * 0.1 -- Simplified
			return dropFactor + (self.elevationAdjustment / 3.44) -- Convert MOA to inches at range
		end,

		_calculateWindageOffset = function(self: SightComponent, environmental: any): number
			-- Simplified wind compensation
			local windOffset = 0
			if environmental and environmental.windSpeed then
				windOffset = environmental.windSpeed * 0.01 -- Simplified wind effect
			end

			return windOffset + (self.windageAdjustment / 3.44) -- Convert MOA to inches
		end,
	}

	return sight
end

-- ============================================================================
-- FACTORY FUNCTION EXPORT
-- ============================================================================

return createSightComponent
