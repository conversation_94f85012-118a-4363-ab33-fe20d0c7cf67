--!strict
--[[
	Gun System Framework - Magazine Component Factory

	This module implements a factory for creating magazine components.
	Magazines handle ammunition storage, feeding, and reliability mechanics.
]]

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type MagazineComponent = Types.MagazineComponent
type ComponentConfig = Types.ComponentConfig
type WeaponStatistics = Types.WeaponStatistics
type Weapon = Types.Weapon
type AttachmentPoint = Types.AttachmentPoint
type ValidationResult = Types.ValidationResult
type InitResult = Types.InitResult
type AttachResult = Types.AttachResult
type DetachResult = Types.DetachResult
type FireData = Types.FireData
type ReloadData = Types.ReloadData
type AmmunitionType = Types.AmmunitionType
type FeedType = Types.FeedType

-- ============================================================================
-- MAGAZINE COMPONENT IMPLEMENTATION
-- ============================================================================

local function createMagazineComponent(config: ComponentConfig): MagazineComponent
	-- Validate magazine-specific configuration
	assert(
		config.customProperties and config.customProperties.capacity,
		"Magazine capacity is required"
	)
	assert(
		config.customProperties and config.customProperties.compatibleAmmo,
		"Compatible ammunition types are required"
	)

	local magazine: MagazineComponent = {
		-- Base component properties
		id = config.id,
		type = "Magazine",
		name = config.name,
		description = config.description or "Standard magazine component",
		version = "1.0.0",

		-- Configuration and stats
		config = config,
		stats = {
			accuracyBonus = 0,
			recoilReduction = 0,
			rangeIncrease = 0,
			damageMultiplier = 1,
			massAddition = config.mass or 0.5,
			lengthAddition = 0,
			durabilityModifier = 1.0,
			jamChanceModifier = 0,
			muzzleFlashReduction = 0,
			soundSuppressionLevel = 0,
			stabilizationBonus = 0,
		},

		-- Dependencies and conflicts
		dependencies = {}, -- Magazines don't depend on other components
		conflicts = { "Magazine" }, -- Only one magazine per weapon
		requiredAttachmentPoint = "magazine_well",

		-- Magazine-specific properties
		capacity = config.customProperties.capacity,
		feedType = config.customProperties.feedType or "Magazine",
		compatibleAmmo = config.customProperties.compatibleAmmo,
		currentAmmo = nil,

		-- Current state
		currentCount = 0,
		ammunition = {},

		-- Feed reliability
		feedReliability = config.customProperties.feedReliability or 0.98,
		springTension = 1.0, -- Full spring tension when new

		-- Magazine wear tracking
		insertionCount = 0,
		dropCount = 0,
		wearLevel = 0,

		-- Custom data
		customData = config.customProperties,

		-- ============================================================================
		-- LIFECYCLE METHODS
		-- ============================================================================

		initialize = function(self: MagazineComponent, initConfig: ComponentConfig): InitResult
			if self.capacity <= 0 then
				return {
					success = false,
					message = "Magazine capacity must be positive",
					warnings = {},
				}
			end

			if #self.compatibleAmmo == 0 then
				return {
					success = false,
					message = "Magazine must be compatible with at least one ammunition type",
					warnings = {},
				}
			end

			local warnings = {}

			if self.capacity > 100 then
				table.insert(warnings, "Very high capacity magazine may affect weapon balance")
			end

			if self.feedReliability < 0.9 then
				table.insert(warnings, "Low feed reliability may cause frequent jams")
			end

			return {
				success = true,
				message = `Magazine component {self.id} initialized successfully`,
				warnings = warnings,
			}
		end,

		validate = function(self: MagazineComponent, weapon: Weapon): ValidationResult
			-- Check if weapon can accept this magazine
			local hasMagazineWell = false
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.name == "magazine_well" then
					hasMagazineWell = true
					break
				end
			end

			if not hasMagazineWell then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Weapon does not have a magazine well",
					suggestedAction = "Reject",
				}
			end

			-- Check ammunition compatibility
			local weaponAmmoCompatible = false
			for _, weaponAmmo in ipairs(weapon.baseStatistics.compatibleAmmo) do
				for _, magAmmo in ipairs(self.compatibleAmmo) do
					if weaponAmmo == magAmmo then
						weaponAmmoCompatible = true
						break
					end
				end
				if weaponAmmoCompatible then
					break
				end
			end

			if not weaponAmmoCompatible then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Magazine is not compatible with weapon ammunition",
					suggestedAction = "Reject",
				}
			end

			return {
				isValid = true,
				severity = "Warning",
				description = "Magazine is compatible with weapon",
				suggestedAction = "Allow",
			}
		end,

		onAttach = function(
			self: MagazineComponent,
			weapon: Weapon,
			attachmentPoint: AttachmentPoint
		): AttachResult
			attachmentPoint.occupied = true
			attachmentPoint.component = self
			self.insertionCount += 1

			-- Calculate wear from insertions
			self.wearLevel = math.min(1.0, self.wearLevel + (self.insertionCount * 0.001))

			return {
				success = true,
				attachmentPoint = attachmentPoint.name,
				modifiedStats = weapon.baseStatistics, -- Magazines don't modify base stats
				conflicts = {},
			}
		end,

		onDetach = function(self: MagazineComponent, weapon: Weapon): DetachResult
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.component == self then
					attachmentPoint.occupied = false
					attachmentPoint.component = nil
					break
				end
			end

			return {
				success = true,
				restoredStats = weapon.baseStatistics,
				dependentComponents = {},
			}
		end,

		-- ============================================================================
		-- RUNTIME INTERFACE METHODS
		-- ============================================================================

		onFire = function(self: MagazineComponent, weapon: Weapon, fireData: FireData): ()
			-- Extract ammunition when weapon fires
			if self.currentCount > 0 then
				self:extractAmmunition()
			end
		end,

		onReload = function(self: MagazineComponent, weapon: Weapon, reloadData: ReloadData): ()
			-- Handle reload operations
			if reloadData.reloadType == "Empty" then
				-- Full reload - load all ammunition
				self:loadAmmunition(reloadData.ammunitionType, reloadData.roundsToLoad)
			elseif reloadData.reloadType == "Tactical" then
				-- Tactical reload - top off magazine
				local remainingCapacity = self:getRemainingCapacity()
				local roundsToAdd = math.min(remainingCapacity, reloadData.roundsToLoad)
				self:loadAmmunition(reloadData.ammunitionType, roundsToAdd)
			end
		end,

		onUpdate = function(self: MagazineComponent, weapon: Weapon, deltaTime: number): ()
			-- Spring tension recovery over time (simplified)
			if self.springTension < 1.0 then
				self.springTension = math.min(1.0, self.springTension + deltaTime * 0.01)
			end
		end,

		-- ============================================================================
		-- MAGAZINE-SPECIFIC METHODS
		-- ============================================================================

		modifyStatistics = function(
			self: MagazineComponent,
			baseStats: WeaponStatistics
		): WeaponStatistics
			-- Magazines typically don't modify weapon statistics directly
			-- But they could affect reload times based on capacity
			local modifiedStats = table.clone(baseStats)

			-- Larger magazines might be slightly slower to reload
			if self.capacity > 30 then
				modifiedStats.reloadTime.tactical *= 1.1
				modifiedStats.reloadTime.empty *= 1.1
			end

			return modifiedStats
		end,

		loadAmmunition = function(self: MagazineComponent, ammo: AmmunitionType, count: number): any
			-- Check if ammunition type is compatible
			local isCompatible = false
			for _, compatibleAmmo in ipairs(self.compatibleAmmo) do
				if ammo == compatibleAmmo then
					isCompatible = true
					break
				end
			end

			if not isCompatible then
				return {
					success = false,
					message = `Ammunition type {ammo} is not compatible with this magazine`,
					roundsLoaded = 0,
				}
			end

			-- Calculate how many rounds can actually be loaded
			local remainingCapacity = self:getRemainingCapacity()
			local roundsToLoad = math.min(count, remainingCapacity)

			-- Load the ammunition
			for i = 1, roundsToLoad do
				table.insert(self.ammunition, ammo)
			end

			self.currentCount += roundsToLoad
			self.currentAmmo = ammo

			return {
				success = true,
				message = `Loaded {roundsToLoad} rounds of {ammo}`,
				roundsLoaded = roundsToLoad,
			}
		end,

		extractAmmunition = function(self: MagazineComponent): AmmunitionType?
			if self:isEmpty() then
				return nil
			end

			-- Check feed reliability
			local feedSuccess = math.random() < self:calculateFeedProbability()
			if not feedSuccess then
				-- Feed failure - round doesn't extract properly
				return nil
			end

			-- Extract the top round
			local extractedAmmo = table.remove(self.ammunition, #self.ammunition)
			self.currentCount -= 1

			-- Update current ammo type
			if self.currentCount > 0 then
				self.currentAmmo = self.ammunition[#self.ammunition]
			else
				self.currentAmmo = nil
			end

			-- Reduce spring tension slightly
			self.springTension = math.max(0.5, self.springTension - 0.001)

			return extractedAmmo
		end,

		getRemainingCapacity = function(self: MagazineComponent): number
			return self.capacity - self.currentCount
		end,

		isEmpty = function(self: MagazineComponent): boolean
			return self.currentCount == 0
		end,

		isFull = function(self: MagazineComponent): boolean
			return self.currentCount >= self.capacity
		end,

		mixAmmunition = function(
			self: MagazineComponent,
			ammoTypes: { AmmunitionType },
			pattern: string
		): boolean
			-- Clear current ammunition
			self.ammunition = {}
			self.currentCount = 0

			-- Load ammunition according to pattern
			-- Pattern examples: "ABAB", "AAAB", etc.
			local patternIndex = 1
			for i = 1, self.capacity do
				if patternIndex > #pattern then
					patternIndex = 1
				end

				local patternChar = string.sub(pattern, patternIndex, patternIndex)
				local ammoIndex = string.byte(patternChar) - string.byte("A") + 1

				if ammoIndex <= #ammoTypes then
					table.insert(self.ammunition, ammoTypes[ammoIndex])
					self.currentCount += 1
				end

				patternIndex += 1
			end

			if self.currentCount > 0 then
				self.currentAmmo = self.ammunition[#self.ammunition]
			end

			return true
		end,

		getTopRound = function(self: MagazineComponent): AmmunitionType?
			if self:isEmpty() then
				return nil
			end
			return self.ammunition[#self.ammunition]
		end,

		calculateFeedProbability = function(self: MagazineComponent): number
			-- Base feed reliability modified by wear and spring tension
			local reliability = self.feedReliability
			reliability *= (1 - self.wearLevel * 0.2) -- Up to 20% reduction from wear
			reliability *= self.springTension -- Spring tension affects feeding

			return math.max(0.5, reliability) -- Minimum 50% reliability
		end,
	}

	return magazine
end

-- ============================================================================
-- FACTORY FUNCTION EXPORT
-- ============================================================================

return createMagazineComponent
