--!strict
--[[
	Gun System Framework - Stock Component Factory

	This module implements a factory for creating stock components.
	Stocks affect weapon recoil, stability, and handling characteristics.
	Supports fixed, collapsible, folding, and adjustable stock types.
]]

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type StockComponent = Types.StockComponent
type ComponentConfig = Types.ComponentConfig
type WeaponStatistics = Types.WeaponStatistics
type Weapon = Types.Weapon
type AttachmentPoint = Types.AttachmentPoint
type ValidationResult = Types.ValidationResult
type InitResult = Types.InitResult
type AttachResult = Types.AttachResult
type DetachResult = Types.DetachResult
type FireData = Types.FireData
type ReloadData = Types.ReloadData
type Vector3 = Types.Vector3

-- ============================================================================
-- STOCK COMPONENT IMPLEMENTATION
-- ============================================================================

local function createStockComponent(config: ComponentConfig): StockComponent
	-- Validate stock-specific configuration
	assert(config.customProperties and config.customProperties.stockType, "Stock type is required")
	assert(
		config.customProperties and config.customProperties.lengthOfPull,
		"Length of pull is required"
	)

	local stock: StockComponent = {
		-- Base component properties
		id = config.id,
		type = "Stock",
		name = config.name,
		description = config.description or "Weapon stock component",
		version = "1.0.0",

		-- Configuration and stats
		config = config,
		stats = {
			accuracyBonus = config.accuracyModifier or 0,
			recoilReduction = config.recoilModifier or 0.15, -- Default 15% recoil reduction
			rangeIncrease = 0,
			damageMultiplier = 1,
			massAddition = config.mass or 0.8,
			lengthAddition = config.length or 25, -- cm
			durabilityModifier = 1.0,
			jamChanceModifier = 0,
			muzzleFlashReduction = 0,
			soundSuppressionLevel = 0,
			stabilizationBonus = config.customProperties.stabilizationBonus or 0.2,
		},

		-- Dependencies and conflicts
		dependencies = {},
		conflicts = { "Stock" }, -- Only one stock per weapon
		requiredAttachmentPoint = "stock_mount",

		-- Stock-specific properties
		stockType = config.customProperties.stockType,
		lengthOfPull = config.customProperties.lengthOfPull,
		combRise = config.customProperties.combRise or 0,
		castOff = config.customProperties.castOff or 0,

		-- Adjustability
		isAdjustable = config.customProperties.isAdjustable or false,
		minLength = config.customProperties.minLength,
		maxLength = config.customProperties.maxLength,
		currentPosition = config.customProperties.currentPosition or 1,

		-- Recoil management
		recoilPadType = config.customProperties.recoilPadType or "Rubber",
		recoilReduction = config.customProperties.recoilReduction or 0.15,

		-- Stock state
		isDeployed = config.customProperties.stockType ~= "Folding" or true,
		adjustmentWear = 0,

		-- Custom data
		customData = config.customProperties,

		-- ============================================================================
		-- LIFECYCLE METHODS
		-- ============================================================================

		initialize = function(self: StockComponent, initConfig: ComponentConfig): InitResult
			if self.lengthOfPull <= 0 then
				return {
					success = false,
					message = "Length of pull must be positive",
					warnings = {},
				}
			end

			-- Validate adjustable stock settings
			if self.isAdjustable then
				if not self.minLength or not self.maxLength then
					return {
						success = false,
						message = "Adjustable stocks require min and max length settings",
						warnings = {},
					}
				end

				if self.minLength >= self.maxLength then
					return {
						success = false,
						message = "Minimum length must be less than maximum length",
						warnings = {},
					}
				end
			end

			local warnings = {}

			-- Check for unusual configurations
			if self.lengthOfPull < 300 then
				table.insert(warnings, "Very short length of pull may affect ergonomics")
			end

			if self.lengthOfPull > 400 then
				table.insert(warnings, "Very long length of pull may affect handling")
			end

			return {
				success = true,
				message = `Stock component {self.id} initialized successfully`,
				warnings = warnings,
			}
		end,

		validate = function(self: StockComponent, weapon: Weapon): ValidationResult
			-- Check if weapon has stock mount
			local hasStockMount = false
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.name == "stock_mount" then
					hasStockMount = true
					break
				end
			end

			if not hasStockMount then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Weapon does not have a stock mount",
					suggestedAction = "Reject",
				}
			end

			-- Check if stock type is appropriate for weapon category
			if weapon.category == "Pistol" then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Stocks are not compatible with pistols",
					suggestedAction = "Reject",
				}
			end

			return {
				isValid = true,
				severity = "Warning",
				description = "Stock is compatible with weapon",
				suggestedAction = "Allow",
			}
		end,

		onAttach = function(
			self: StockComponent,
			weapon: Weapon,
			attachmentPoint: AttachmentPoint
		): AttachResult
			attachmentPoint.occupied = true
			attachmentPoint.component = self

			-- Calculate modified stats
			local modifiedStats = self:modifyStatistics(weapon.baseStatistics)

			return {
				success = true,
				attachmentPoint = attachmentPoint.name,
				modifiedStats = modifiedStats,
				conflicts = {},
			}
		end,

		onDetach = function(self: StockComponent, weapon: Weapon): DetachResult
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.component == self then
					attachmentPoint.occupied = false
					attachmentPoint.component = nil
					break
				end
			end

			return {
				success = true,
				restoredStats = weapon.baseStatistics,
				dependentComponents = {},
			}
		end,

		-- ============================================================================
		-- RUNTIME INTERFACE METHODS
		-- ============================================================================

		onFire = function(self: StockComponent, weapon: Weapon, fireData: FireData): ()
			-- Stocks help absorb recoil energy
			-- Increase wear slightly with each shot
			if self.isAdjustable then
				self.adjustmentWear = math.min(1.0, self.adjustmentWear + 0.00001)
			end
		end,

		onReload = function(self: StockComponent, weapon: Weapon, reloadData: ReloadData): ()
			-- No specific reload behavior for stocks
		end,

		onUpdate = function(self: StockComponent, weapon: Weapon, deltaTime: number): ()
			-- No continuous updates needed for stocks
		end,

		-- ============================================================================
		-- STOCK-SPECIFIC METHODS
		-- ============================================================================

		modifyStatistics = function(
			self: StockComponent,
			baseStats: WeaponStatistics
		): WeaponStatistics
			local modifiedStats = table.clone(baseStats)

			-- Recoil reduction based on stock type and recoil pad
			local recoilReduction = self.recoilReduction

			-- Recoil pad type affects reduction
			if self.recoilPadType == "Gel" then
				recoilReduction *= 1.2
			elseif self.recoilPadType == "Hard" then
				recoilReduction *= 0.8
			elseif self.recoilPadType == "None" then
				recoilReduction *= 0.5
			end

			-- Apply recoil reduction
			modifiedStats.recoil.vertical *= (1 - recoilReduction)
			modifiedStats.recoil.horizontal *= (1 - recoilReduction * 0.5) -- Less effect on horizontal

			-- Stock type affects handling
			if self.stockType == "Collapsible" then
				-- Slightly less stable but more maneuverable
				modifiedStats.recoil.recovery *= 1.1
			elseif self.stockType == "Fixed" then
				-- More stable
				modifiedStats.accuracy *= 1.05
			elseif self.stockType == "Folding" then
				if not self.isDeployed then
					-- Folded stock significantly affects accuracy and recoil
					modifiedStats.accuracy *= 0.7
					modifiedStats.recoil.vertical *= 1.5
					modifiedStats.recoil.horizontal *= 1.3
				end
			end

			-- Adjustable stocks with wear are less effective
			if self.isAdjustable and self.adjustmentWear > 0.5 then
				local wearPenalty = self.adjustmentWear * 0.1
				modifiedStats.recoil.vertical *= (1 + wearPenalty)
			end

			return modifiedStats
		end,

		modifyRecoil = function(self: StockComponent, baseRecoil: Vector3): Vector3
			if not self.isDeployed then
				-- Folded stock increases recoil significantly
				return {
					X = baseRecoil.X * 1.3,
					Y = baseRecoil.Y * 1.5,
					Z = baseRecoil.Z,
				}
			end

			-- Calculate recoil reduction
			local reduction = self.recoilReduction

			-- Adjust for recoil pad type
			if self.recoilPadType == "Gel" then
				reduction *= 1.2
			elseif self.recoilPadType == "Hard" then
				reduction *= 0.8
			end

			return {
				X = baseRecoil.X * (1 - reduction * 0.5), -- Less effect on horizontal
				Y = baseRecoil.Y * (1 - reduction), -- Full effect on vertical
				Z = baseRecoil.Z,
			}
		end,

		modifyStability = function(self: StockComponent, baseStability: number): number
			if not self.isDeployed then
				return baseStability * 0.6 -- Significant stability loss when folded
			end

			local stabilityBonus = 0.2 -- Base stability bonus

			-- Stock type affects stability
			if self.stockType == "Fixed" then
				stabilityBonus *= 1.2
			elseif self.stockType == "Collapsible" then
				stabilityBonus *= 0.9
			end

			-- Adjustable stocks with wear are less stable
			if self.isAdjustable and self.adjustmentWear > 0.3 then
				stabilityBonus *= (1 - self.adjustmentWear * 0.3)
			end

			return baseStability * (1 + stabilityBonus)
		end,

		adjustLength = function(self: StockComponent, newPosition: number): boolean
			if not self.isAdjustable then
				return false
			end

			if not self.minLength or not self.maxLength then
				return false
			end

			-- Validate position
			if newPosition < 1 or newPosition > (self.maxLength - self.minLength + 1) then
				return false
			end

			self.currentPosition = newPosition

			-- Calculate new length of pull
			local positionRange = self.maxLength - self.minLength
			local positionFactor = (newPosition - 1) / math.max(1, positionRange)
			self.lengthOfPull = self.minLength + (positionFactor * positionRange)

			-- Add slight wear from adjustment
			self.adjustmentWear = math.min(1.0, self.adjustmentWear + 0.001)

			return true
		end,

		-- Toggle folding stock
		toggleDeployment = function(self: StockComponent): boolean
			if self.stockType ~= "Folding" then
				return false
			end

			self.isDeployed = not self.isDeployed
			return true
		end,
	}

	return stock
end

-- ============================================================================
-- FACTORY FUNCTION EXPORT
-- ============================================================================

return createStockComponent
