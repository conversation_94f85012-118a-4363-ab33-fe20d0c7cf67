--!strict
--[[
	Gun System Framework - Weapon Customization UI

	This module provides a comprehensive user interface for weapon customization:
	- 3D weapon inspection and rotation
	- Component attachment/detachment interface
	- Real-time statistics visualization
	- Component compatibility checking
	- Inventory management integration
]]

local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

-- ============================================================================
-- WEAPON CUSTOMIZATION UI
-- ============================================================================

local WeaponCustomizationUI = {}
WeaponCustomizationUI.__index = WeaponCustomizationUI

-- UI State
local uiState = {
  isOpen = false,
  currentWeapon = nil,
  selectedComponent = nil,
  selectedAttachmentPoint = nil,
  rotationEnabled = false,
  lastMousePosition = nil,
}

-- UI Elements
local screenGui = nil
local mainFrame = nil
local weaponViewport = nil
local statsPanel = nil
local componentsPanel = nil
local attachmentPointsPanel = nil

-- 3D Weapon Display
local weaponModel = nil
local weaponCamera = nil
local weaponRotation = { X = 0, Y = 0, Z = 0 }

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function WeaponCustomizationUI.initialize(): ()
  print("[WeaponCustomizationUI] Initializing weapon customization interface...")

  -- Create UI elements
  WeaponCustomizationUI._createUI()

  -- Set up input handling
  WeaponCustomizationUI._setupInputHandling()

  print("[WeaponCustomizationUI] Weapon customization UI initialized")
end

function WeaponCustomizationUI._createUI(): ()
  local player = Players.LocalPlayer :: Player
  local playerGui = player:WaitForChild("PlayerGui")

  -- Create main ScreenGui
  screenGui = Instance.new("ScreenGui")
  screenGui.Name = "WeaponCustomizationUI"
  screenGui.ResetOnSpawn = false
  screenGui.Enabled = false
  screenGui.Parent = playerGui

  -- Main frame
  mainFrame = Instance.new("Frame")
  mainFrame.Name = "MainFrame"
  mainFrame.Size = UDim2.new(0.9, 0, 0.8, 0)
  mainFrame.Position = UDim2.new(0.05, 0, 0.1, 0)
  mainFrame.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
  mainFrame.BorderSizePixel = 0
  mainFrame.Parent = screenGui

  -- Add corner radius
  local corner = Instance.new("UICorner")
  corner.CornerRadius = UDim.new(0, 12)
  corner.Parent = mainFrame

  -- Create panels
  WeaponCustomizationUI._createWeaponViewport()
  WeaponCustomizationUI._createStatsPanel()
  WeaponCustomizationUI._createComponentsPanel()
  WeaponCustomizationUI._createAttachmentPointsPanel()
  WeaponCustomizationUI._createControlButtons()
end

function WeaponCustomizationUI._createWeaponViewport(): ()
  -- Weapon 3D viewport
  weaponViewport = Instance.new("ViewportFrame")
  weaponViewport.Name = "WeaponViewport"
  weaponViewport.Size = UDim2.new(0.5, -10, 0.7, -10)
  weaponViewport.Position = UDim2.new(0, 10, 0, 10)
  weaponViewport.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
  weaponViewport.BorderSizePixel = 0
  weaponViewport.Parent = mainFrame

  -- Viewport corner
  local viewportCorner = Instance.new("UICorner")
  viewportCorner.CornerRadius = UDim.new(0, 8)
  viewportCorner.Parent = weaponViewport

  -- Create camera for viewport
  weaponCamera = Instance.new("Camera")
  weaponCamera.CFrame = CFrame.new(Vector3.new(0, 0, 5), Vector3.new(0, 0, 0))
  weaponViewport.CurrentCamera = weaponCamera

  -- Viewport title
  local viewportTitle = Instance.new("TextLabel")
  viewportTitle.Name = "ViewportTitle"
  viewportTitle.Size = UDim2.new(1, 0, 0, 30)
  viewportTitle.Position = UDim2.new(0, 0, 0, -35)
  viewportTitle.BackgroundTransparency = 1
  viewportTitle.Text = "Weapon Inspection"
  viewportTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
  viewportTitle.TextSize = 18
  viewportTitle.Font = Enum.Font.SourceSansBold
  viewportTitle.Parent = weaponViewport
end

function WeaponCustomizationUI._createStatsPanel(): ()
  -- Statistics panel
  statsPanel = Instance.new("ScrollingFrame")
  statsPanel.Name = "StatsPanel"
  statsPanel.Size = UDim2.new(0.25, -10, 0.7, -10)
  statsPanel.Position = UDim2.new(0.5, 5, 0, 10)
  statsPanel.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
  statsPanel.BorderSizePixel = 0
  statsPanel.ScrollBarThickness = 6
  statsPanel.Parent = mainFrame

  -- Stats panel corner
  local statsCorner = Instance.new("UICorner")
  statsCorner.CornerRadius = UDim.new(0, 8)
  statsCorner.Parent = statsPanel

  -- Stats layout
  local statsLayout = Instance.new("UIListLayout")
  statsLayout.SortOrder = Enum.SortOrder.LayoutOrder
  statsLayout.Padding = UDim.new(0, 5)
  statsLayout.Parent = statsPanel

  -- Stats title
  local statsTitle = Instance.new("TextLabel")
  statsTitle.Name = "StatsTitle"
  statsTitle.Size = UDim2.new(1, 0, 0, 30)
  statsTitle.BackgroundTransparency = 1
  statsTitle.Text = "Weapon Statistics"
  statsTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
  statsTitle.TextSize = 16
  statsTitle.Font = Enum.Font.SourceSansBold
  statsTitle.LayoutOrder = 1
  statsTitle.Parent = statsPanel
end

function WeaponCustomizationUI._createComponentsPanel(): ()
  -- Components panel
  componentsPanel = Instance.new("ScrollingFrame")
  componentsPanel.Name = "ComponentsPanel"
  componentsPanel.Size = UDim2.new(0.25, -10, 0.7, -10)
  componentsPanel.Position = UDim2.new(0.75, 5, 0, 10)
  componentsPanel.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
  componentsPanel.BorderSizePixel = 0
  componentsPanel.ScrollBarThickness = 6
  componentsPanel.Parent = mainFrame

  -- Components panel corner
  local componentsCorner = Instance.new("UICorner")
  componentsCorner.CornerRadius = UDim.new(0, 8)
  componentsCorner.Parent = componentsPanel

  -- Components layout
  local componentsLayout = Instance.new("UIListLayout")
  componentsLayout.SortOrder = Enum.SortOrder.LayoutOrder
  componentsLayout.Padding = UDim.new(0, 5)
  componentsLayout.Parent = componentsPanel

  -- Components title
  local componentsTitle = Instance.new("TextLabel")
  componentsTitle.Name = "ComponentsTitle"
  componentsTitle.Size = UDim2.new(1, 0, 0, 30)
  componentsTitle.BackgroundTransparency = 1
  componentsTitle.Text = "Available Components"
  componentsTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
  componentsTitle.TextSize = 16
  componentsTitle.Font = Enum.Font.SourceSansBold
  componentsTitle.LayoutOrder = 1
  componentsTitle.Parent = componentsPanel
end

function WeaponCustomizationUI._createAttachmentPointsPanel(): ()
  -- Attachment points panel
  attachmentPointsPanel = Instance.new("Frame")
  attachmentPointsPanel.Name = "AttachmentPointsPanel"
  attachmentPointsPanel.Size = UDim2.new(1, -20, 0.25, -10)
  attachmentPointsPanel.Position = UDim2.new(0, 10, 0.75, 5)
  attachmentPointsPanel.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
  attachmentPointsPanel.BorderSizePixel = 0
  attachmentPointsPanel.Parent = mainFrame

  -- Attachment points corner
  local attachmentCorner = Instance.new("UICorner")
  attachmentCorner.CornerRadius = UDim.new(0, 8)
  attachmentCorner.Parent = attachmentPointsPanel

  -- Attachment points layout
  local attachmentLayout = Instance.new("UIListLayout")
  attachmentLayout.SortOrder = Enum.SortOrder.LayoutOrder
  attachmentLayout.FillDirection = Enum.FillDirection.Horizontal
  attachmentLayout.Padding = UDim.new(0, 10)
  attachmentLayout.Parent = attachmentPointsPanel

  -- Attachment points title
  local attachmentTitle = Instance.new("TextLabel")
  attachmentTitle.Name = "AttachmentTitle"
  attachmentTitle.Size = UDim2.new(1, 0, 0, 25)
  attachmentTitle.Position = UDim2.new(0, 0, 0, -30)
  attachmentTitle.BackgroundTransparency = 1
  attachmentTitle.Text = "Attachment Points"
  attachmentTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
  attachmentTitle.TextSize = 16
  attachmentTitle.Font = Enum.Font.SourceSansBold
  attachmentTitle.Parent = attachmentPointsPanel
end

function WeaponCustomizationUI._createControlButtons(): ()
  -- Close button
  local closeButton = Instance.new("TextButton")
  closeButton.Name = "CloseButton"
  closeButton.Size = UDim2.new(0, 100, 0, 40)
  closeButton.Position = UDim2.new(1, -110, 0, 10)
  closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
  closeButton.Text = "Close"
  closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
  closeButton.TextSize = 16
  closeButton.Font = Enum.Font.SourceSansBold
  closeButton.Parent = mainFrame

  local closeCorner = Instance.new("UICorner")
  closeCorner.CornerRadius = UDim.new(0, 6)
  closeCorner.Parent = closeButton

  closeButton.MouseButton1Click:Connect(function()
    WeaponCustomizationUI.closeUI()
  end)

  -- Reset button
  local resetButton = Instance.new("TextButton")
  resetButton.Name = "ResetButton"
  resetButton.Size = UDim2.new(0, 100, 0, 40)
  resetButton.Position = UDim2.new(1, -220, 0, 10)
  resetButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
  resetButton.Text = "Reset View"
  resetButton.TextColor3 = Color3.fromRGB(255, 255, 255)
  resetButton.TextSize = 16
  resetButton.Font = Enum.Font.SourceSansBold
  resetButton.Parent = mainFrame

  local resetCorner = Instance.new("UICorner")
  resetCorner.CornerRadius = UDim.new(0, 6)
  resetCorner.Parent = resetButton

  resetButton.MouseButton1Click:Connect(function()
    WeaponCustomizationUI._resetWeaponView()
  end)
end

-- ============================================================================
-- UI CONTROL FUNCTIONS
-- ============================================================================

function WeaponCustomizationUI.openUI(weapon: Weapon): ()
  if not weapon then
    warn("[WeaponCustomizationUI] No weapon provided")
    return
  end

  uiState.currentWeapon = weapon
  uiState.isOpen = true

  -- Show UI
  screenGui.Enabled = true

  -- Load weapon into viewport
  WeaponCustomizationUI._loadWeaponModel(weapon)

  -- Update panels
  WeaponCustomizationUI._updateStatsPanel()
  WeaponCustomizationUI._updateComponentsPanel()
  WeaponCustomizationUI._updateAttachmentPointsPanel()

  -- Animate UI entrance
  mainFrame.Position = UDim2.new(0.05, 0, 1.1, 0)
  local openTween = TweenService:Create(
    mainFrame,
    TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
    { Position = UDim2.new(0.05, 0, 0.1, 0) }
  )
  openTween:Play()

  print(`[WeaponCustomizationUI] Opened customization UI for {weapon.name}`)
end

function WeaponCustomizationUI.closeUI(): ()
  if not uiState.isOpen then
    return
  end

  uiState.isOpen = false

  -- Animate UI exit
  local closeTween = TweenService:Create(
    mainFrame,
    TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
    { Position = UDim2.new(0.05, 0, 1.1, 0) }
  )

  closeTween:Play()
  closeTween.Completed:Connect(function()
    screenGui.Enabled = false
    WeaponCustomizationUI._cleanupWeaponModel()
  end)

  print("[WeaponCustomizationUI] Closed customization UI")
end

function WeaponCustomizationUI.isUIOpen(): boolean
  return uiState.isOpen
end

-- ============================================================================
-- WEAPON MODEL MANAGEMENT
-- ============================================================================

function WeaponCustomizationUI._loadWeaponModel(weapon: Weapon): ()
  -- Clean up existing model
  WeaponCustomizationUI._cleanupWeaponModel()

  -- Create weapon model (simplified - would need actual 3D models)
  weaponModel = Instance.new("Model")
  weaponModel.Name = weapon.name
  weaponModel.Parent = weaponViewport

  -- Create basic weapon representation
  local weaponPart = Instance.new("Part")
  weaponPart.Name = "WeaponBody"
  weaponPart.Size = Vector3.new(4, 0.5, 0.2)
  weaponPart.Material = Enum.Material.Metal
  weaponPart.Color = Color3.fromRGB(60, 60, 60)
  weaponPart.Anchored = true
  weaponPart.Parent = weaponModel

  -- Add attachment point indicators
  WeaponCustomizationUI._createAttachmentPointIndicators(weapon)

  -- Position camera
  weaponCamera.CFrame = CFrame.new(Vector3.new(0, 0, 8), Vector3.new(0, 0, 0))

  -- Reset rotation
  weaponRotation = { X = 0, Y = 0, Z = 0 }
  WeaponCustomizationUI._updateWeaponRotation()
end

function WeaponCustomizationUI._createAttachmentPointIndicators(weapon: Weapon): ()
  -- Create visual indicators for attachment points
  for pointName, point in pairs(weapon.attachmentPoints) do
    local indicator = Instance.new("Part")
    indicator.Name = `AttachmentPoint_{pointName}`
    indicator.Size = Vector3.new(0.3, 0.3, 0.3)
    indicator.Shape = Enum.PartType.Ball
    indicator.Material = Enum.Material.Neon
    indicator.Color = point.occupied and Color3.fromRGB(255, 100, 100)
      or Color3.fromRGB(100, 255, 100)
    indicator.Anchored = true
    indicator.Position = Vector3.new(point.position.X, point.position.Y, point.position.Z)
    indicator.Parent = weaponModel

    -- Add click detection (simplified)
    local clickDetector = Instance.new("ClickDetector")
    clickDetector.MaxActivationDistance = 50
    clickDetector.Parent = indicator

    clickDetector.MouseClick:Connect(function()
      WeaponCustomizationUI._selectAttachmentPoint(pointName)
    end)
  end
end

function WeaponCustomizationUI._cleanupWeaponModel(): ()
  if weaponModel then
    weaponModel:Destroy()
    weaponModel = nil
  end
end

function WeaponCustomizationUI._updateWeaponRotation(): ()
  if not weaponModel then
    return
  end

  local rotation = CFrame.Angles(
    math.rad(weaponRotation.X),
    math.rad(weaponRotation.Y),
    math.rad(weaponRotation.Z)
  )

  for _, part in pairs(weaponModel:GetChildren()) do
    if part:IsA("BasePart") then
      part.CFrame = rotation * CFrame.new(part.Position)
    end
  end
end

function WeaponCustomizationUI._resetWeaponView(): ()
  weaponRotation = { X = 0, Y = 0, Z = 0 }
  WeaponCustomizationUI._updateWeaponRotation()
  weaponCamera.CFrame = CFrame.new(Vector3.new(0, 0, 8), Vector3.new(0, 0, 0))
end

-- ============================================================================
-- PANEL UPDATE FUNCTIONS
-- ============================================================================

function WeaponCustomizationUI._updateStatsPanel(): ()
  if not uiState.currentWeapon then
    return
  end

  -- Clear existing stats
  for _, child in pairs(statsPanel:GetChildren()) do
    if child:IsA("Frame") and child.Name:find("StatBar") then
      child:Destroy()
    end
  end

  local weapon = uiState.currentWeapon
  local baseStats = weapon.baseStatistics
  local effectiveStats = weapon:getEffectiveStatistics()

  -- Create stat bars
  local statOrder = 2
  local stats = {
    { name = "Damage", base = baseStats.damage, effective = effectiveStats.damage, max = 100 },
    {
      name = "Accuracy",
      base = baseStats.accuracy * 100,
      effective = effectiveStats.accuracy * 100,
      max = 100,
    },
    { name = "Range", base = baseStats.range, effective = effectiveStats.range, max = 1000 },
    {
      name = "Fire Rate",
      base = baseStats.fireRate,
      effective = effectiveStats.fireRate,
      max = 1000,
    },
    {
      name = "Recoil Control",
      base = (1 - baseStats.recoil.vertical) * 100,
      effective = (1 - effectiveStats.recoil.vertical) * 100,
      max = 100,
    },
  }

  for _, stat in ipairs(stats) do
    WeaponCustomizationUI._createStatBar(stat.name, stat.base, stat.effective, stat.max, statOrder)
    statOrder += 1
  end

  -- Update canvas size
  statsPanel.CanvasSize = UDim2.new(0, 0, 0, statOrder * 60)
end

function WeaponCustomizationUI._createStatBar(
  name: string,
  baseValue: number,
  effectiveValue: number,
  maxValue: number,
  order: number
): ()
  local statFrame = Instance.new("Frame")
  statFrame.Name = `StatBar_{name}`
  statFrame.Size = UDim2.new(1, -10, 0, 50)
  statFrame.BackgroundTransparency = 1
  statFrame.LayoutOrder = order
  statFrame.Parent = statsPanel

  -- Stat name
  local statLabel = Instance.new("TextLabel")
  statLabel.Size = UDim2.new(1, 0, 0, 20)
  statLabel.BackgroundTransparency = 1
  statLabel.Text = name
  statLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
  statLabel.TextSize = 14
  statLabel.Font = Enum.Font.SourceSans
  statLabel.TextXAlignment = Enum.TextXAlignment.Left
  statLabel.Parent = statFrame

  -- Base stat bar
  local baseBar = Instance.new("Frame")
  baseBar.Size = UDim2.new(baseValue / maxValue, 0, 0, 8)
  baseBar.Position = UDim2.new(0, 0, 0, 22)
  baseBar.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
  baseBar.BorderSizePixel = 0
  baseBar.Parent = statFrame

  -- Effective stat bar
  local effectiveBar = Instance.new("Frame")
  effectiveBar.Size = UDim2.new(effectiveValue / maxValue, 0, 0, 8)
  effectiveBar.Position = UDim2.new(0, 0, 0, 32)
  effectiveBar.BackgroundColor3 = effectiveValue > baseValue and Color3.fromRGB(100, 255, 100)
    or Color3.fromRGB(255, 100, 100)
  effectiveBar.BorderSizePixel = 0
  effectiveBar.Parent = statFrame

  -- Stat values
  local valueLabel = Instance.new("TextLabel")
  valueLabel.Size = UDim2.new(1, 0, 0, 15)
  valueLabel.Position = UDim2.new(0, 0, 0, 42)
  valueLabel.BackgroundTransparency = 1
  valueLabel.Text = `{math.floor(baseValue)} → {math.floor(effectiveValue)}`
  valueLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
  valueLabel.TextSize = 12
  valueLabel.Font = Enum.Font.SourceSans
  valueLabel.TextXAlignment = Enum.TextXAlignment.Left
  valueLabel.Parent = statFrame
end

function WeaponCustomizationUI._updateComponentsPanel(): ()
  if not uiState.currentWeapon then
    return
  end

  -- Clear existing components
  for _, child in pairs(componentsPanel:GetChildren()) do
    if child:IsA("Frame") and child.Name:find("Component") then
      child:Destroy()
    end
  end

  -- Get available components (simplified - would come from inventory)
  local availableComponents = WeaponCustomizationUI._getAvailableComponents()

  local componentOrder = 2
  for _, component in ipairs(availableComponents) do
    WeaponCustomizationUI._createComponentButton(component, componentOrder)
    componentOrder += 1
  end

  -- Update canvas size
  componentsPanel.CanvasSize = UDim2.new(0, 0, 0, componentOrder * 70)
end

function WeaponCustomizationUI._createComponentButton(component: any, order: number): ()
  local componentFrame = Instance.new("Frame")
  componentFrame.Name = `Component_{component.id}`
  componentFrame.Size = UDim2.new(1, -10, 0, 60)
  componentFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
  componentFrame.BorderSizePixel = 0
  componentFrame.LayoutOrder = order
  componentFrame.Parent = componentsPanel

  local componentCorner = Instance.new("UICorner")
  componentCorner.CornerRadius = UDim.new(0, 6)
  componentCorner.Parent = componentFrame

  -- Component name
  local nameLabel = Instance.new("TextLabel")
  nameLabel.Size = UDim2.new(1, -10, 0, 20)
  nameLabel.Position = UDim2.new(0, 5, 0, 5)
  nameLabel.BackgroundTransparency = 1
  nameLabel.Text = component.name
  nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
  nameLabel.TextSize = 14
  nameLabel.Font = Enum.Font.SourceSansBold
  nameLabel.TextXAlignment = Enum.TextXAlignment.Left
  nameLabel.Parent = componentFrame

  -- Component type
  local typeLabel = Instance.new("TextLabel")
  typeLabel.Size = UDim2.new(1, -10, 0, 15)
  typeLabel.Position = UDim2.new(0, 5, 0, 25)
  typeLabel.BackgroundTransparency = 1
  typeLabel.Text = component.type
  typeLabel.TextColor3 = Color3.fromRGB(150, 150, 150)
  typeLabel.TextSize = 12
  typeLabel.Font = Enum.Font.SourceSans
  typeLabel.TextXAlignment = Enum.TextXAlignment.Left
  typeLabel.Parent = componentFrame

  -- Attach button
  local attachButton = Instance.new("TextButton")
  attachButton.Size = UDim2.new(0, 60, 0, 25)
  attachButton.Position = UDim2.new(1, -65, 0, 30)
  attachButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
  attachButton.Text = "Attach"
  attachButton.TextColor3 = Color3.fromRGB(255, 255, 255)
  attachButton.TextSize = 12
  attachButton.Font = Enum.Font.SourceSansBold
  attachButton.Parent = componentFrame

  local attachCorner = Instance.new("UICorner")
  attachCorner.CornerRadius = UDim.new(0, 4)
  attachCorner.Parent = attachButton

  attachButton.MouseButton1Click:Connect(function()
    WeaponCustomizationUI._attachComponent(component)
  end)
end

function WeaponCustomizationUI._updateAttachmentPointsPanel(): ()
  if not uiState.currentWeapon then
    return
  end

  -- Clear existing attachment points
  for _, child in pairs(attachmentPointsPanel:GetChildren()) do
    if child:IsA("Frame") and child.Name:find("AttachmentPoint") then
      child:Destroy()
    end
  end

  local weapon = uiState.currentWeapon
  local pointOrder = 1

  for pointName, point in pairs(weapon.attachmentPoints) do
    WeaponCustomizationUI._createAttachmentPointButton(pointName, point, pointOrder)
    pointOrder += 1
  end
end

function WeaponCustomizationUI._createAttachmentPointButton(
  pointName: string,
  point: any,
  order: number
): ()
  local pointFrame = Instance.new("Frame")
  pointFrame.Name = `AttachmentPoint_{pointName}`
  pointFrame.Size = UDim2.new(0, 150, 1, -30)
  pointFrame.BackgroundColor3 = point.occupied and Color3.fromRGB(60, 40, 40)
    or Color3.fromRGB(40, 60, 40)
  pointFrame.BorderSizePixel = 0
  pointFrame.LayoutOrder = order
  pointFrame.Parent = attachmentPointsPanel

  local pointCorner = Instance.new("UICorner")
  pointCorner.CornerRadius = UDim.new(0, 6)
  pointCorner.Parent = pointFrame

  -- Point name
  local nameLabel = Instance.new("TextLabel")
  nameLabel.Size = UDim2.new(1, -10, 0, 20)
  nameLabel.Position = UDim2.new(0, 5, 0, 5)
  nameLabel.BackgroundTransparency = 1
  nameLabel.Text = pointName:gsub("_", " "):upper()
  nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
  nameLabel.TextSize = 12
  nameLabel.Font = Enum.Font.SourceSansBold
  nameLabel.TextXAlignment = Enum.TextXAlignment.Left
  nameLabel.Parent = pointFrame

  -- Component info
  local componentLabel = Instance.new("TextLabel")
  componentLabel.Size = UDim2.new(1, -10, 0, 15)
  componentLabel.Position = UDim2.new(0, 5, 0, 25)
  componentLabel.BackgroundTransparency = 1
  componentLabel.Text = point.occupied and point.component.name or "Empty"
  componentLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
  componentLabel.TextSize = 10
  componentLabel.Font = Enum.Font.SourceSans
  componentLabel.TextXAlignment = Enum.TextXAlignment.Left
  componentLabel.Parent = pointFrame

  -- Action button
  if point.occupied then
    local detachButton = Instance.new("TextButton")
    detachButton.Size = UDim2.new(0, 50, 0, 20)
    detachButton.Position = UDim2.new(1, -55, 0, 45)
    detachButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
    detachButton.Text = "Remove"
    detachButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    detachButton.TextSize = 10
    detachButton.Font = Enum.Font.SourceSansBold
    detachButton.Parent = pointFrame

    local detachCorner = Instance.new("UICorner")
    detachCorner.CornerRadius = UDim.new(0, 3)
    detachCorner.Parent = detachButton

    detachButton.MouseButton1Click:Connect(function()
      WeaponCustomizationUI._detachComponent(pointName)
    end)
  end
end

-- ============================================================================
-- INPUT HANDLING
-- ============================================================================

function WeaponCustomizationUI._setupInputHandling(): ()
  -- Mouse input for weapon rotation
  weaponViewport.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
      uiState.rotationEnabled = true
      uiState.lastMousePosition = input.Position
    end
  end)

  weaponViewport.InputEnded:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
      uiState.rotationEnabled = false
    end
  end)

  weaponViewport.InputChanged:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseMovement and uiState.rotationEnabled then
      if uiState.lastMousePosition then
        local delta = input.Position - uiState.lastMousePosition
        weaponRotation.Y += delta.X * 0.5
        weaponRotation.X -= delta.Y * 0.5

        -- Clamp rotation
        weaponRotation.X = math.max(-90, math.min(90, weaponRotation.X))

        WeaponCustomizationUI._updateWeaponRotation()
      end
      uiState.lastMousePosition = input.Position
    end
  end)

  -- Keyboard shortcuts
  UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed or not uiState.isOpen then
      return
    end

    if input.KeyCode == Enum.KeyCode.Escape then
      WeaponCustomizationUI.closeUI()
    elseif input.KeyCode == Enum.KeyCode.R then
      WeaponCustomizationUI._resetWeaponView()
    end
  end)
end

-- ============================================================================
-- COMPONENT MANAGEMENT
-- ============================================================================

function WeaponCustomizationUI._attachComponent(component: any): ()
  if not uiState.currentWeapon then
    return
  end

  -- Find compatible attachment point
  local compatiblePoint = WeaponCustomizationUI._findCompatibleAttachmentPoint(component)
  if not compatiblePoint then
    warn(`No compatible attachment point for {component.type}`)
    return
  end

  -- Attach component
  local result = uiState.currentWeapon:attach(component, compatiblePoint)
  if result.success then
    print(`Attached {component.name} to {compatiblePoint}`)

    -- Update UI
    WeaponCustomizationUI._updateStatsPanel()
    WeaponCustomizationUI._updateAttachmentPointsPanel()
    WeaponCustomizationUI._loadWeaponModel(uiState.currentWeapon) -- Refresh 3D model
  else
    warn(`Failed to attach component: {result.errorReason}`)
  end
end

function WeaponCustomizationUI._detachComponent(attachmentPointName: string): ()
  if not uiState.currentWeapon then
    return
  end

  local attachmentPoint = uiState.currentWeapon.attachmentPoints[attachmentPointName]
  if not attachmentPoint or not attachmentPoint.occupied then
    return
  end

  local component = attachmentPoint.component
  local result = uiState.currentWeapon:detach(component)

  if result.success then
    print(`Detached {component.name} from {attachmentPointName}`)

    -- Update UI
    WeaponCustomizationUI._updateStatsPanel()
    WeaponCustomizationUI._updateAttachmentPointsPanel()
    WeaponCustomizationUI._loadWeaponModel(uiState.currentWeapon) -- Refresh 3D model
  else
    warn(`Failed to detach component: {result.errorReason}`)
  end
end

function WeaponCustomizationUI._selectAttachmentPoint(pointName: string): ()
  uiState.selectedAttachmentPoint = pointName
  print(`Selected attachment point: {pointName}`)

  -- Highlight selected point (visual feedback)
  WeaponCustomizationUI._highlightAttachmentPoint(pointName)
end

function WeaponCustomizationUI._highlightAttachmentPoint(pointName: string): ()
  if not weaponModel then
    return
  end

  -- Remove previous highlights
  for _, part in pairs(weaponModel:GetChildren()) do
    if part.Name:find("AttachmentPoint_") then
      part.Color = part.Name == `AttachmentPoint_{pointName}` and Color3.fromRGB(255, 255, 100)
        or (
          part.Name:find("AttachmentPoint_") and Color3.fromRGB(100, 255, 100)
          or Color3.fromRGB(255, 100, 100)
        )
    end
  end
end

function WeaponCustomizationUI._findCompatibleAttachmentPoint(component: any): string?
  if not uiState.currentWeapon then
    return nil
  end

  -- Simple compatibility check based on component type
  local compatibilityMap = {
    Sight = "optic_rail",
    Stock = "stock_mount",
    Grip = "foregrip_rail",
    Suppressor = "muzzle_device",
    Barrel = "barrel_mount",
    Magazine = "magazine_well",
  }

  local requiredPoint = compatibilityMap[component.type]
  if requiredPoint and uiState.currentWeapon.attachmentPoints[requiredPoint] then
    return requiredPoint
  end

  return nil
end

function WeaponCustomizationUI._getAvailableComponents(): { any }
  -- Simplified component list - would come from player inventory
  return {
    { id = "red_dot_1", name = "Red Dot Sight", type = "Sight" },
    { id = "acog_1", name = "ACOG 4x Scope", type = "Sight" },
    { id = "fixed_stock_1", name = "Fixed Stock", type = "Stock" },
    { id = "collapsible_stock_1", name = "Collapsible Stock", type = "Stock" },
    { id = "vertical_grip_1", name = "Vertical Grip", type = "Grip" },
    { id = "suppressor_1", name = "Sound Suppressor", type = "Suppressor" },
  }
end

return WeaponCustomizationUI
