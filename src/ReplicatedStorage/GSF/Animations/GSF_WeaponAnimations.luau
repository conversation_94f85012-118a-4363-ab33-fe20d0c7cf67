--!strict
--[[
	Gun System Framework - Weapon Animations
	
	This module contains per-weapon CFrame-based animation configurations:
	- Weapon-specific recoil patterns
	- Reload animation sequences
	- Inspection animations
	- ADS (Aim Down Sights) animations
	- Fire mode switch animations
]]

-- ============================================================================
-- WEAPON ANIMATION CONFIGURATIONS
-- ============================================================================

local WeaponAnimations = {}

-- Base animation configuration template
local BASE_ANIMATION_CONFIG = {
	-- Recoil settings
	recoil = {
		intensity = 1.0,
		pattern = "random", -- "random", "vertical", "horizontal", "custom"
		duration = 0.1,
		recovery = 0.3,
		randomness = 0.2,
		verticalMultiplier = 1.0,
		horizontalMultiplier = 0.5,
	},
	
	-- Reload animation
	reload = {
		duration = 2.0,
		sequence = {}, -- Will be filled per weapon
		smoothness = 0.8,
	},
	
	-- ADS animation
	ads = {
		duration = 0.3,
		offset = CFrame.new(0, 0, 0.2),
		smoothness = 0.9,
	},
	
	-- Inspection animation
	inspection = {
		duration = 3.0,
		sequence = {}, -- Will be filled per weapon
		smoothness = 0.7,
	},
	
	-- Fire mode switch
	fireModeSwitch = {
		duration = 0.5,
		offset = CFrame.new(0, 0, 0.1) * CFrame.Angles(math.rad(-5), 0, 0),
		smoothness = 0.8,
	},
}

-- ============================================================================
-- ASSAULT RIFLE ANIMATIONS
-- ============================================================================

WeaponAnimations.M4A1 = {
	recoil = {
		intensity = 1.2,
		pattern = "vertical",
		duration = 0.08,
		recovery = 0.25,
		randomness = 0.15,
		verticalMultiplier = 1.2,
		horizontalMultiplier = 0.3,
		customPattern = {
			{ X = 0, Y = 0.1, Z = 0 },
			{ X = 0.02, Y = 0.08, Z = 0 },
			{ X = -0.01, Y = 0.06, Z = 0 },
			{ X = 0.01, Y = 0.04, Z = 0 },
		},
	},
	
	reload = {
		duration = 2.5,
		sequence = {
			{
				time = 0.3,
				cframe = CFrame.new(0, -0.2, 0) * CFrame.Angles(math.rad(10), 0, 0),
				description = "Lower weapon"
			},
			{
				time = 0.5,
				cframe = CFrame.new(-0.1, -0.3, 0) * CFrame.Angles(math.rad(15), math.rad(-10), 0),
				description = "Magazine release"
			},
			{
				time = 0.8,
				cframe = CFrame.new(-0.2, -0.4, 0) * CFrame.Angles(math.rad(20), math.rad(-15), 0),
				description = "Magazine out"
			},
			{
				time = 0.6,
				cframe = CFrame.new(0.1, -0.3, 0) * CFrame.Angles(math.rad(15), math.rad(10), 0),
				description = "New magazine"
			},
			{
				time = 0.3,
				cframe = CFrame.new(0, -0.1, 0) * CFrame.Angles(math.rad(5), 0, 0),
				description = "Magazine insert"
			},
			{
				time = 0.5,
				cframe = CFrame.new(0, 0, 0),
				description = "Return to ready"
			},
		},
		smoothness = 0.8,
	},
	
	ads = {
		duration = 0.25,
		offset = CFrame.new(0, 0.05, 0.3) * CFrame.Angles(math.rad(-2), 0, 0),
		smoothness = 0.9,
	},
	
	inspection = {
		duration = 4.0,
		sequence = {
			{
				time = 0.5,
				cframe = CFrame.new(0, 0, 0.2) * CFrame.Angles(0, math.rad(15), 0),
				description = "Turn right"
			},
			{
				time = 1.0,
				cframe = CFrame.new(0, 0, 0.2) * CFrame.Angles(0, math.rad(-15), 0),
				description = "Turn left"
			},
			{
				time = 1.0,
				cframe = CFrame.new(0, 0.1, 0.3) * CFrame.Angles(math.rad(-10), 0, 0),
				description = "Lift up"
			},
			{
				time = 1.0,
				cframe = CFrame.new(0.2, 0, 0.2) * CFrame.Angles(0, 0, math.rad(15)),
				description = "Tilt right"
			},
			{
				time = 0.5,
				cframe = CFrame.new(0, 0, 0),
				description = "Return to ready"
			},
		},
		smoothness = 0.7,
	},
}

WeaponAnimations.AK47 = {
	recoil = {
		intensity = 1.5,
		pattern = "custom",
		duration = 0.1,
		recovery = 0.35,
		randomness = 0.25,
		verticalMultiplier = 1.4,
		horizontalMultiplier = 0.6,
		customPattern = {
			{ X = 0, Y = 0.12, Z = 0 },
			{ X = 0.03, Y = 0.1, Z = 0 },
			{ X = -0.02, Y = 0.08, Z = 0 },
			{ X = 0.04, Y = 0.06, Z = 0 },
			{ X = -0.03, Y = 0.04, Z = 0 },
		},
	},
	
	reload = {
		duration = 2.8,
		sequence = {
			{
				time = 0.4,
				cframe = CFrame.new(0, -0.25, 0) * CFrame.Angles(math.rad(12), 0, 0),
				description = "Lower weapon"
			},
			{
				time = 0.6,
				cframe = CFrame.new(-0.15, -0.35, 0) * CFrame.Angles(math.rad(18), math.rad(-12), 0),
				description = "Magazine release"
			},
			{
				time = 0.9,
				cframe = CFrame.new(-0.25, -0.45, 0) * CFrame.Angles(math.rad(25), math.rad(-18), 0),
				description = "Magazine out"
			},
			{
				time = 0.7,
				cframe = CFrame.new(0.15, -0.35, 0) * CFrame.Angles(math.rad(18), math.rad(12), 0),
				description = "New magazine"
			},
			{
				time = 0.2,
				cframe = CFrame.new(0, -0.15, 0) * CFrame.Angles(math.rad(8), 0, 0),
				description = "Magazine insert"
			},
			{
				time = 0.6,
				cframe = CFrame.new(0, 0, 0),
				description = "Return to ready"
			},
		},
		smoothness = 0.75,
	},
	
	ads = {
		duration = 0.3,
		offset = CFrame.new(0, 0.03, 0.25) * CFrame.Angles(math.rad(-1), 0, 0),
		smoothness = 0.85,
	},
}

-- ============================================================================
-- SNIPER RIFLE ANIMATIONS
-- ============================================================================

WeaponAnimations.AWP = {
	recoil = {
		intensity = 2.5,
		pattern = "vertical",
		duration = 0.15,
		recovery = 0.8,
		randomness = 0.1,
		verticalMultiplier = 2.0,
		horizontalMultiplier = 0.2,
	},
	
	reload = {
		duration = 3.5,
		sequence = {
			{
				time = 0.5,
				cframe = CFrame.new(0, -0.1, 0) * CFrame.Angles(math.rad(5), 0, 0),
				description = "Bolt up"
			},
			{
				time = 0.8,
				cframe = CFrame.new(0, -0.2, -0.2) * CFrame.Angles(math.rad(10), 0, 0),
				description = "Bolt back"
			},
			{
				time = 1.0,
				cframe = CFrame.new(0, -0.3, 0) * CFrame.Angles(math.rad(15), 0, 0),
				description = "Shell eject"
			},
			{
				time = 0.8,
				cframe = CFrame.new(0, -0.2, 0) * CFrame.Angles(math.rad(10), 0, 0),
				description = "New round"
			},
			{
				time = 0.4,
				cframe = CFrame.new(0, 0, 0),
				description = "Bolt forward and down"
			},
		},
		smoothness = 0.6,
	},
	
	ads = {
		duration = 0.4,
		offset = CFrame.new(0, 0.1, 0.4) * CFrame.Angles(math.rad(-3), 0, 0),
		smoothness = 0.95,
	},
	
	inspection = {
		duration = 5.0,
		sequence = {
			{
				time = 1.0,
				cframe = CFrame.new(0, 0, 0.3) * CFrame.Angles(0, math.rad(20), 0),
				description = "Turn right slowly"
			},
			{
				time = 1.5,
				cframe = CFrame.new(0, 0, 0.3) * CFrame.Angles(0, math.rad(-20), 0),
				description = "Turn left slowly"
			},
			{
				time = 1.5,
				cframe = CFrame.new(0, 0.15, 0.4) * CFrame.Angles(math.rad(-15), 0, 0),
				description = "Lift up for scope view"
			},
			{
				time = 1.0,
				cframe = CFrame.new(0, 0, 0),
				description = "Return to ready"
			},
		},
		smoothness = 0.8,
	},
}

-- ============================================================================
-- PISTOL ANIMATIONS
-- ============================================================================

WeaponAnimations.Glock17 = {
	recoil = {
		intensity = 0.8,
		pattern = "vertical",
		duration = 0.06,
		recovery = 0.2,
		randomness = 0.3,
		verticalMultiplier = 0.8,
		horizontalMultiplier = 0.4,
	},
	
	reload = {
		duration = 1.8,
		sequence = {
			{
				time = 0.2,
				cframe = CFrame.new(0, -0.15, 0) * CFrame.Angles(math.rad(8), 0, 0),
				description = "Lower weapon"
			},
			{
				time = 0.3,
				cframe = CFrame.new(-0.1, -0.2, 0) * CFrame.Angles(math.rad(12), math.rad(-8), 0),
				description = "Magazine release"
			},
			{
				time = 0.5,
				cframe = CFrame.new(-0.15, -0.3, 0) * CFrame.Angles(math.rad(15), math.rad(-12), 0),
				description = "Magazine out"
			},
			{
				time = 0.4,
				cframe = CFrame.new(0.1, -0.2, 0) * CFrame.Angles(math.rad(12), math.rad(8), 0),
				description = "New magazine"
			},
			{
				time = 0.4,
				cframe = CFrame.new(0, 0, 0),
				description = "Return to ready"
			},
		},
		smoothness = 0.9,
	},
	
	ads = {
		duration = 0.2,
		offset = CFrame.new(0, 0.02, 0.15) * CFrame.Angles(math.rad(-1), 0, 0),
		smoothness = 0.95,
	},
	
	inspection = {
		duration = 2.5,
		sequence = {
			{
				time = 0.4,
				cframe = CFrame.new(0, 0, 0.1) * CFrame.Angles(0, math.rad(25), 0),
				description = "Turn right"
			},
			{
				time = 0.6,
				cframe = CFrame.new(0, 0, 0.1) * CFrame.Angles(0, math.rad(-25), 0),
				description = "Turn left"
			},
			{
				time = 0.8,
				cframe = CFrame.new(0, 0.08, 0.15) * CFrame.Angles(math.rad(-8), 0, 0),
				description = "Lift up"
			},
			{
				time = 0.7,
				cframe = CFrame.new(0, 0, 0),
				description = "Return to ready"
			},
		},
		smoothness = 0.8,
	},
}

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function WeaponAnimations.getAnimationConfig(weaponName: string): any?
	return WeaponAnimations[weaponName]
end

function WeaponAnimations.hasCustomAnimations(weaponName: string): boolean
	return WeaponAnimations[weaponName] ~= nil
end

function WeaponAnimations.getRecoilPattern(weaponName: string): { any }?
	local config = WeaponAnimations[weaponName]
	if config and config.recoil and config.recoil.customPattern then
		return config.recoil.customPattern
	end
	return nil
end

function WeaponAnimations.getReloadSequence(weaponName: string): { any }?
	local config = WeaponAnimations[weaponName]
	if config and config.reload and config.reload.sequence then
		return config.reload.sequence
	end
	return nil
end

function WeaponAnimations.getInspectionSequence(weaponName: string): { any }?
	local config = WeaponAnimations[weaponName]
	if config and config.inspection and config.inspection.sequence then
		return config.inspection.sequence
	end
	return nil
end

function WeaponAnimations.getADSConfig(weaponName: string): any?
	local config = WeaponAnimations[weaponName]
	if config and config.ads then
		return config.ads
	end
	return nil
end

function WeaponAnimations.createDefaultConfig(weaponName: string): any
	-- Create a default configuration for weapons without custom animations
	local defaultConfig = table.clone(BASE_ANIMATION_CONFIG)
	
	-- Adjust based on weapon category (if we can determine it)
	if weaponName:find("Pistol") or weaponName:find("Glock") then
		defaultConfig.recoil.intensity = 0.8
		defaultConfig.reload.duration = 1.8
	elseif weaponName:find("Sniper") or weaponName:find("AWP") then
		defaultConfig.recoil.intensity = 2.0
		defaultConfig.reload.duration = 3.0
	elseif weaponName:find("Rifle") or weaponName:find("M4") or weaponName:find("AK") then
		defaultConfig.recoil.intensity = 1.2
		defaultConfig.reload.duration = 2.5
	end
	
	return defaultConfig
end

return WeaponAnimations
