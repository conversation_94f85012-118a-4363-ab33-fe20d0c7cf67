--!strict
--[[
	Gun System Framework - Enhanced Object Pool System

	This module provides advanced object pooling for performance optimization:
	- Multiple pool types with different strategies
	- Automatic pool size management
	- Memory usage monitoring
	- Pool statistics and analytics
	- Configurable cleanup policies
]]

local RunService = game:GetService("RunService")

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- ============================================================================
-- OBJECT POOL SYSTEM
-- ============================================================================

local ObjectPool = {}
ObjectPool.__index = ObjectPool

-- Pool types
local POOL_TYPES = {
  FIXED = "Fixed", -- Fixed size pool
  DYNAMIC = "Dynamic", -- Grows and shrinks based on demand
  CIRCULAR = "Circular", -- Circular buffer with fixed size
  PRIORITY = "Priority", -- Priority-based allocation
}

-- Pool configurations
local poolConfigs = {
  [POOL_TYPES.FIXED] = {
    initialSize = 10,
    maxSize = 10,
    canGrow = false,
    canShrink = false,
  },

  [POOL_TYPES.DYNAMIC] = {
    initialSize = 5,
    maxSize = 100,
    minSize = 2,
    canGrow = true,
    canShrink = true,
    growthFactor = 1.5,
    shrinkThreshold = 0.25,
  },

  [POOL_TYPES.CIRCULAR] = {
    initialSize = 20,
    maxSize = 20,
    canGrow = false,
    canShrink = false,
    overwriteOldest = true,
  },

  [POOL_TYPES.PRIORITY] = {
    initialSize = 10,
    maxSize = 50,
    canGrow = true,
    canShrink = true,
    priorityLevels = 3,
  },
}

-- Active pools
local activePools = {}
local poolStatistics = {}

-- Global pool settings
local globalSettings = {
  enableStatistics = true,
  enableAutoCleanup = true,
  cleanupInterval = 30, -- seconds
  maxTotalObjects = 1000,
  memoryThreshold = 100, -- MB
}

-- ============================================================================
-- POOL CREATION AND MANAGEMENT
-- ============================================================================

function ObjectPool.createPool(
  poolName: string,
  poolType: string,
  factory: () -> any,
  config: { [string]: any }?
): boolean
  if activePools[poolName] then
    warn(`[ObjectPool] Pool '{poolName}' already exists`)
    return false
  end

  local poolConfig = config or poolConfigs[poolType] or poolConfigs[POOL_TYPES.DYNAMIC]

  local pool = {
    name = poolName,
    type = poolType,
    factory = factory,
    config = poolConfig,
    objects = {},
    available = {},
    inUse = {},
    created = 0,
    allocated = 0,
    deallocated = 0,
    hits = 0,
    misses = 0,
    lastCleanup = tick(),
    priority = config and config.priority or 1,
  }

  -- Pre-populate pool
  ObjectPool._populatePool(pool, poolConfig.initialSize)

  activePools[poolName] = pool
  poolStatistics[poolName] = {
    totalAllocations = 0,
    totalDeallocations = 0,
    peakUsage = 0,
    hitRate = 0,
    averageLifetime = 0,
    memoryUsage = 0,
  }

  print(`[ObjectPool] Created {poolType} pool '{poolName}' with {poolConfig.initialSize} objects`)
  return true
end

function ObjectPool.destroyPool(poolName: string): boolean
  local pool = activePools[poolName]
  if not pool then
    warn(`[ObjectPool] Pool '{poolName}' does not exist`)
    return false
  end

  -- Clean up all objects
  ObjectPool._cleanupPool(pool)

  activePools[poolName] = nil
  poolStatistics[poolName] = nil

  print(`[ObjectPool] Destroyed pool '{poolName}'`)
  return true
end

-- ============================================================================
-- OBJECT ALLOCATION AND DEALLOCATION
-- ============================================================================

function ObjectPool.allocate(poolName: string): any?
  local pool = activePools[poolName]
  if not pool then
    warn(`[ObjectPool] Pool '{poolName}' does not exist`)
    return nil
  end

  local object = ObjectPool._allocateFromPool(pool)

  if object then
    pool.hits += 1
    pool.allocated += 1
    poolStatistics[poolName].totalAllocations += 1
    poolStatistics[poolName].peakUsage = math.max(poolStatistics[poolName].peakUsage, #pool.inUse)
  else
    pool.misses += 1
  end

  -- Update hit rate
  local total = pool.hits + pool.misses
  poolStatistics[poolName].hitRate = total > 0 and (pool.hits / total) or 0

  return object
end

function ObjectPool.deallocate(poolName: string, object: any): boolean
  local pool = activePools[poolName]
  if not pool then
    warn(`[ObjectPool] Pool '{poolName}' does not exist`)
    return false
  end

  return ObjectPool._deallocateToPool(pool, object)
end

function ObjectPool._allocateFromPool(pool: any): any?
  -- Try to get from available objects
  if #pool.available > 0 then
    local object = table.remove(pool.available)
    table.insert(pool.inUse, object)

    -- Reset object state
    ObjectPool._resetObject(object, pool.type)

    return object
  end

  -- Try to grow pool if possible
  if pool.config.canGrow and #pool.objects < pool.config.maxSize then
    local newObject = pool.factory()
    if newObject then
      table.insert(pool.objects, newObject)
      table.insert(pool.inUse, newObject)
      pool.created += 1

      return newObject
    end
  end

  -- Handle circular buffer overflow
  if pool.type == POOL_TYPES.CIRCULAR and pool.config.overwriteOldest and #pool.inUse > 0 then
    local oldestObject = table.remove(pool.inUse, 1)
    ObjectPool._resetObject(oldestObject, pool.type)
    table.insert(pool.inUse, oldestObject)
    return oldestObject
  end

  return nil
end

function ObjectPool._deallocateToPool(pool: any, object: any): boolean
  -- Find and remove from in-use list
  for i, inUseObject in ipairs(pool.inUse) do
    if inUseObject == object then
      table.remove(pool.inUse, i)

      -- Add to available list
      table.insert(pool.available, object)

      pool.deallocated += 1
      poolStatistics[pool.name].totalDeallocations += 1

      return true
    end
  end

  warn(`[ObjectPool] Object not found in pool '{pool.name}'`)
  return false
end

-- ============================================================================
-- POOL MAINTENANCE
-- ============================================================================

function ObjectPool._populatePool(pool: any, count: number): ()
  for i = 1, count do
    local object = pool.factory()
    if object then
      table.insert(pool.objects, object)
      table.insert(pool.available, object)
      pool.created += 1
    else
      warn(`[ObjectPool] Failed to create object for pool '{pool.name}'`)
      break
    end
  end
end

function ObjectPool._resetObject(object: any, poolType: string): ()
  -- Reset common properties
  if object.Position then
    object.Position = Vector3.new(0, 0, 0)
  end

  if object.Velocity then
    object.Velocity = Vector3.new(0, 0, 0)
  end

  if object.Transparency then
    object.Transparency = 0
  end

  if object.Size then
    object.Size = Vector3.new(1, 1, 1)
  end

  -- Pool-specific resets
  if poolType == POOL_TYPES.PRIORITY then
    if object.Priority then
      object.Priority = 1
    end
  end

  -- Reset parent
  if object.Parent then
    object.Parent = nil
  end
end

function ObjectPool._cleanupPool(pool: any): ()
  -- Destroy all objects
  for _, object in ipairs(pool.objects) do
    if object and object.Destroy then
      object:Destroy()
    end
  end

  -- Clear arrays
  pool.objects = {}
  pool.available = {}
  pool.inUse = {}
end

-- ============================================================================
-- AUTOMATIC POOL MANAGEMENT
-- ============================================================================

function ObjectPool.initialize(): ()
  print("[ObjectPool] Initializing object pool system...")

  -- Start automatic cleanup
  if globalSettings.enableAutoCleanup then
    RunService.Heartbeat:Connect(ObjectPool._autoCleanup)
  end

  print("[ObjectPool] Object pool system initialized")
end

function ObjectPool._autoCleanup(): ()
  local currentTime = tick()

  for poolName, pool in pairs(activePools) do
    if currentTime - pool.lastCleanup >= globalSettings.cleanupInterval then
      ObjectPool._performPoolMaintenance(pool)
      pool.lastCleanup = currentTime
    end
  end
end

function ObjectPool._performPoolMaintenance(pool: any): ()
  -- Dynamic pool shrinking
  if pool.type == POOL_TYPES.DYNAMIC and pool.config.canShrink then
    local usageRatio = #pool.inUse / #pool.objects

    if usageRatio < pool.config.shrinkThreshold and #pool.objects > pool.config.minSize then
      local targetSize = math.max(pool.config.minSize, math.ceil(#pool.objects * 0.8))
      ObjectPool._shrinkPool(pool, targetSize)
    end
  end

  -- Memory pressure cleanup
  local memoryUsage = collectgarbage("count") / 1024 -- Convert to MB
  if memoryUsage > globalSettings.memoryThreshold then
    ObjectPool._performMemoryCleanup()
  end
end

function ObjectPool._shrinkPool(pool: any, targetSize: number): ()
  local currentSize = #pool.objects
  local toRemove = currentSize - targetSize

  if toRemove <= 0 then
    return
  end

  -- Remove from available objects first
  local removed = 0
  while removed < toRemove and #pool.available > 0 do
    local object = table.remove(pool.available)

    -- Remove from objects array
    for i, obj in ipairs(pool.objects) do
      if obj == object then
        table.remove(pool.objects, i)
        break
      end
    end

    -- Destroy object
    if object and object.Destroy then
      object:Destroy()
    end

    removed += 1
  end

  if removed > 0 then
    print(`[ObjectPool] Shrunk pool '{pool.name}' by {removed} objects`)
  end
end

function ObjectPool._performMemoryCleanup(): ()
  print("[ObjectPool] Performing memory cleanup due to high usage")

  -- Note: Forced garbage collection is not available in Roblox's sandboxed environment
  -- Focus on shrinking pools instead

  -- Shrink all dynamic pools
  for poolName, pool in pairs(activePools) do
    if pool.type == POOL_TYPES.DYNAMIC and pool.config.canShrink then
      local targetSize = math.max(pool.config.minSize, math.ceil(#pool.objects * 0.5))
      ObjectPool._shrinkPool(pool, targetSize)
    end
  end
end

-- ============================================================================
-- STATISTICS AND MONITORING
-- ============================================================================

function ObjectPool.getPoolStatistics(poolName: string): { [string]: any }?
  local pool = activePools[poolName]
  local stats = poolStatistics[poolName]

  if not pool or not stats then
    return nil
  end

  return {
    name = pool.name,
    type = pool.type,
    totalObjects = #pool.objects,
    availableObjects = #pool.available,
    inUseObjects = #pool.inUse,
    totalCreated = pool.created,
    totalAllocated = pool.allocated,
    totalDeallocated = pool.deallocated,
    hitRate = stats.hitRate,
    peakUsage = stats.peakUsage,
    efficiency = #pool.objects > 0 and (#pool.inUse / #pool.objects) or 0,
    memoryEstimate = ObjectPool._estimatePoolMemory(pool),
  }
end

function ObjectPool.getAllPoolStatistics(): { [string]: any }
  local allStats = {}

  for poolName, _ in pairs(activePools) do
    allStats[poolName] = ObjectPool.getPoolStatistics(poolName)
  end

  return allStats
end

function ObjectPool._estimatePoolMemory(pool: any): number
  -- Rough estimate of memory usage (in KB)
  local objectCount = #pool.objects
  local estimatedSizePerObject = 1 -- KB (rough estimate)

  -- Adjust based on pool type
  if pool.name:find("effect") then
    estimatedSizePerObject = 2
  elseif pool.name:find("audio") then
    estimatedSizePerObject = 5
  elseif pool.name:find("projectile") then
    estimatedSizePerObject = 0.5
  end

  return objectCount * estimatedSizePerObject
end

function ObjectPool.printPoolReport(): ()
  local allStats = ObjectPool.getAllPoolStatistics()

  print("\n🏊 === OBJECT POOL REPORT ===")

  local totalObjects = 0
  local totalMemory = 0

  for poolName, stats in pairs(allStats) do
    print(`\nPool: {poolName} ({stats.type})`)
    print(
      `  Objects: {stats.inUseObjects}/{stats.totalObjects} ({math.floor(stats.efficiency * 100)}% efficiency)`
    )
    print(`  Hit Rate: {math.floor(stats.hitRate * 100)}%`)
    print(`  Peak Usage: {stats.peakUsage}`)
    print(`  Memory: ~{stats.memoryEstimate}KB`)

    totalObjects += stats.totalObjects
    totalMemory += stats.memoryEstimate
  end

  print(`\nTotal Objects: {totalObjects}`)
  print(`Total Memory: ~{totalMemory}KB`)
  print(`Active Pools: {ObjectPool.getActivePoolCount()}`)

  -- Performance assessment
  local avgHitRate = ObjectPool._calculateAverageHitRate()
  if avgHitRate > 0.9 then
    print("Performance: Excellent")
  elseif avgHitRate > 0.7 then
    print("Performance: Good")
  elseif avgHitRate > 0.5 then
    print("Performance: Acceptable")
  else
    print("Performance: Poor - Consider increasing pool sizes")
  end

  print("=== END POOL REPORT ===\n")
end

function ObjectPool._calculateAverageHitRate(): number
  local totalHitRate = 0
  local poolCount = 0

  for poolName, _ in pairs(activePools) do
    local stats = ObjectPool.getPoolStatistics(poolName)
    if stats then
      totalHitRate += stats.hitRate
      poolCount += 1
    end
  end

  return poolCount > 0 and (totalHitRate / poolCount) or 0
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function ObjectPool.getActivePoolCount(): number
  local count = 0
  for _ in pairs(activePools) do
    count += 1
  end
  return count
end

function ObjectPool.poolExists(poolName: string): boolean
  return activePools[poolName] ~= nil
end

function ObjectPool.getPoolNames(): { string }
  local names = {}
  for poolName, _ in pairs(activePools) do
    table.insert(names, poolName)
  end
  return names
end

function ObjectPool.setGlobalSettings(settings: { [string]: any }): ()
  for key, value in pairs(settings) do
    if globalSettings[key] ~= nil then
      globalSettings[key] = value
      print(`[ObjectPool] Global setting {key} set to {value}`)
    end
  end
end

function ObjectPool.forceCleanupAll(): ()
  print("[ObjectPool] Forcing cleanup of all pools...")

  for poolName, pool in pairs(activePools) do
    ObjectPool._performPoolMaintenance(pool)
  end

  -- Note: Forced garbage collection is not available in Roblox's sandboxed environment
  local currentMemory = collectgarbage("count")
  print(
    string.format("[ObjectPool] Cleanup complete. Current memory: %dKB", math.floor(currentMemory))
  )
end

-- ============================================================================
-- PRESET POOL CONFIGURATIONS
-- ============================================================================

function ObjectPool.createStandardPools(): ()
  print("[ObjectPool] Creating standard pools...")

  -- Muzzle flash effects pool
  ObjectPool.createPool("muzzle_flash", POOL_TYPES.CIRCULAR, function()
    local flash = Instance.new("Part")
    flash.Name = "MuzzleFlash"
    flash.Material = Enum.Material.Neon
    flash.Shape = Enum.PartType.Ball
    flash.Size = Vector3.new(0.5, 0.5, 0.5)
    flash.Anchored = true
    flash.CanCollide = false
    return flash
  end, { initialSize = 15, maxSize = 30 })

  -- Shell casing pool
  ObjectPool.createPool("shell_casing", POOL_TYPES.DYNAMIC, function()
    local casing = Instance.new("Part")
    casing.Name = "ShellCasing"
    casing.Material = Enum.Material.Metal
    casing.Shape = Enum.PartType.Cylinder
    casing.Size = Vector3.new(0.1, 0.05, 0.05)
    casing.CanCollide = true
    return casing
  end, { initialSize = 20, maxSize = 100 })

  -- Audio source pool
  ObjectPool.createPool("audio_source", POOL_TYPES.FIXED, function()
    local sound = Instance.new("Sound")
    sound.Volume = 0.5
    sound.RollOffMode = Enum.RollOffMode.InverseTapered
    return sound
  end, { initialSize = 25, maxSize = 50 })

  -- Impact effect pool
  ObjectPool.createPool("impact_effect", POOL_TYPES.CIRCULAR, function()
    local effect = Instance.new("Part")
    effect.Name = "ImpactEffect"
    effect.Material = Enum.Material.Neon
    effect.Shape = Enum.PartType.Ball
    effect.Size = Vector3.new(0.2, 0.2, 0.2)
    effect.Anchored = true
    effect.CanCollide = false
    return effect
  end, { initialSize = 10, maxSize = 25 })

  print("[ObjectPool] Standard pools created")
end

return ObjectPool
