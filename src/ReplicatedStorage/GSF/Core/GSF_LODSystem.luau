--!strict
--[[
	Gun System Framework - Level of Detail (LOD) System

	This module provides automatic quality scaling based on:
	- Distance from camera/player
	- Performance metrics
	- Device capabilities
	- User preferences

	Optimizes:
	- Visual effects quality
	- Audio detail levels
	- Component update frequencies
	- Physics simulation accuracy
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type Vector3 = Types.Vector3
type LODLevel = Types.LODLevel
type LODSettings = Types.LODSettings

-- ============================================================================
-- LOD SYSTEM IMPLEMENTATION
-- ============================================================================

local LODSystem = {}
LODSystem.__index = LODSystem

-- LOD levels
local LOD_LEVELS = {
  ULTRA = 0,
  HIGH = 1,
  MEDIUM = 2,
  LOW = 3,
  MINIMAL = 4,
}

-- LOD settings for different quality levels
local lodSettings = {
  [LOD_LEVELS.ULTRA] = {
    name = "Ultra",
    maxDistance = 1000,
    effectsQuality = 1.0,
    audioQuality = 1.0,
    physicsAccuracy = 1.0,
    updateFrequency = 1.0,
    particleCount = 1.0,
    textureQuality = 1.0,
  },

  [LOD_LEVELS.HIGH] = {
    name = "High",
    maxDistance = 750,
    effectsQuality = 0.8,
    audioQuality = 0.9,
    physicsAccuracy = 0.9,
    updateFrequency = 0.8,
    particleCount = 0.8,
    textureQuality = 0.9,
  },

  [LOD_LEVELS.MEDIUM] = {
    name = "Medium",
    maxDistance = 500,
    effectsQuality = 0.6,
    audioQuality = 0.7,
    physicsAccuracy = 0.7,
    updateFrequency = 0.6,
    particleCount = 0.6,
    textureQuality = 0.7,
  },

  [LOD_LEVELS.LOW] = {
    name = "Low",
    maxDistance = 300,
    effectsQuality = 0.4,
    audioQuality = 0.5,
    physicsAccuracy = 0.5,
    updateFrequency = 0.4,
    particleCount = 0.4,
    textureQuality = 0.5,
  },

  [LOD_LEVELS.MINIMAL] = {
    name = "Minimal",
    maxDistance = 150,
    effectsQuality = 0.2,
    audioQuality = 0.3,
    physicsAccuracy = 0.3,
    updateFrequency = 0.2,
    particleCount = 0.2,
    textureQuality = 0.3,
  },
}

-- Current LOD state
local lodState = {
  globalLODLevel = LOD_LEVELS.HIGH,
  adaptiveEnabled = true,
  performanceTarget = 60, -- Target FPS
  lastPerformanceCheck = 0,
  performanceHistory = {},
  distanceBasedEnabled = true,
  playerPosition = Vector3.new(0, 0, 0),
}

-- Registered LOD objects
local lodObjects = {}
local objectCounter = 0

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function LODSystem.initialize(): ()
  print("[LODSystem] Initializing Level of Detail system...")

  -- Start LOD update loop
  RunService.Heartbeat:Connect(LODSystem._updateLOD)

  -- Start performance monitoring for adaptive LOD
  RunService.Heartbeat:Connect(LODSystem._monitorPerformance)

  print("[LODSystem] LOD system initialized")
end

-- ============================================================================
-- LOD OBJECT REGISTRATION
-- ============================================================================

function LODSystem.registerObject(object: any, objectType: string, position: Vector3): string
  objectCounter += 1
  local objectId = `lod_object_{objectCounter}`

  lodObjects[objectId] = {
    id = objectId,
    object = object,
    type = objectType,
    position = position,
    currentLOD = LOD_LEVELS.HIGH,
    lastUpdate = 0,
    distance = 0,
    isActive = true,
  }

  return objectId
end

function LODSystem.unregisterObject(objectId: string): ()
  lodObjects[objectId] = nil
end

function LODSystem.updateObjectPosition(objectId: string, position: Vector3): ()
  local lodObject = lodObjects[objectId]
  if lodObject then
    lodObject.position = position
  end
end

-- ============================================================================
-- LOD LEVEL MANAGEMENT
-- ============================================================================

function LODSystem.setGlobalLODLevel(level: number): ()
  if lodSettings[level] then
    lodState.globalLODLevel = level
    print(`[LODSystem] Global LOD level set to {lodSettings[level].name}`)

    -- Update all objects immediately
    LODSystem._updateAllObjects()
  else
    warn(`[LODSystem] Invalid LOD level: {level}`)
  end
end

function LODSystem.enableAdaptiveLOD(enabled: boolean): ()
  lodState.adaptiveEnabled = enabled
  print(`[LODSystem] Adaptive LOD {enabled and "enabled" or "disabled"}`)
end

function LODSystem.setPerformanceTarget(targetFPS: number): ()
  lodState.performanceTarget = targetFPS
  print(`[LODSystem] Performance target set to {targetFPS} FPS`)
end

function LODSystem.enableDistanceBasedLOD(enabled: boolean): ()
  lodState.distanceBasedEnabled = enabled
  print(`[LODSystem] Distance-based LOD {enabled and "enabled" or "disabled"}`)
end

-- ============================================================================
-- LOD CALCULATION
-- ============================================================================

function LODSystem.calculateLODLevel(position: Vector3, objectType: string): number
  local baseLOD = lodState.globalLODLevel

  -- Distance-based LOD adjustment
  if lodState.distanceBasedEnabled then
    local distance = LODSystem._calculateDistance(position, lodState.playerPosition)
    baseLOD = LODSystem._getDistanceBasedLOD(distance, objectType)
  end

  -- Performance-based LOD adjustment
  if lodState.adaptiveEnabled then
    local performanceLOD = LODSystem._getPerformanceBasedLOD()
    baseLOD = math.max(baseLOD, performanceLOD)
  end

  return math.min(LOD_LEVELS.MINIMAL, baseLOD)
end

function LODSystem._getDistanceBasedLOD(distance: number, objectType: string): number
  -- Different object types have different distance thresholds
  local thresholds = {
    weapon = { 50, 100, 200, 400 },
    effect = { 25, 50, 100, 200 },
    audio = { 30, 75, 150, 300 },
    projectile = { 100, 200, 400, 800 },
  }

  local objectThresholds = thresholds[objectType] or thresholds.effect

  for i, threshold in ipairs(objectThresholds) do
    if distance <= threshold then
      return i - 1 -- Convert to 0-based LOD level
    end
  end

  return LOD_LEVELS.MINIMAL
end

function LODSystem._getPerformanceBasedLOD(): number
  if #lodState.performanceHistory < 10 then
    return lodState.globalLODLevel
  end

  -- Calculate average FPS from recent history
  local totalFPS = 0
  for _, fps in ipairs(lodState.performanceHistory) do
    totalFPS += fps
  end
  local averageFPS = totalFPS / #lodState.performanceHistory

  -- Adjust LOD based on performance
  local targetFPS = lodState.performanceTarget

  if averageFPS >= targetFPS * 1.2 then
    -- Performance is excellent, can use higher quality
    return math.max(0, lodState.globalLODLevel - 1)
  elseif averageFPS < targetFPS * 0.8 then
    -- Performance is poor, need to reduce quality
    return math.min(LOD_LEVELS.MINIMAL, lodState.globalLODLevel + 1)
  elseif averageFPS < targetFPS * 0.6 then
    -- Performance is very poor, aggressive reduction
    return math.min(LOD_LEVELS.MINIMAL, lodState.globalLODLevel + 2)
  end

  return lodState.globalLODLevel
end

-- ============================================================================
-- LOD APPLICATION
-- ============================================================================

function LODSystem.applyLODToEffect(effect: any, lodLevel: number): ()
  local settings = lodSettings[lodLevel]
  if not settings then
    return
  end

  -- Apply effects quality scaling
  if effect.Size then
    effect.Size = effect.Size * settings.effectsQuality
  end

  if effect.Transparency then
    effect.Transparency = 1 - ((1 - effect.Transparency) * settings.effectsQuality)
  end

  -- Reduce particle count
  if effect:FindFirstChild("ParticleEmitter") then
    local emitter = effect.ParticleEmitter
    emitter.Rate = emitter.Rate * settings.particleCount
  end

  -- Adjust lifetime
  if effect.Lifetime then
    effect.Lifetime = effect.Lifetime * settings.updateFrequency
  end
end

function LODSystem.applyLODToAudio(sound: any, lodLevel: number): ()
  local settings = lodSettings[lodLevel]
  if not settings then
    return
  end

  -- Adjust audio quality
  if sound.Volume then
    sound.Volume = sound.Volume * settings.audioQuality
  end

  -- Reduce audio range at lower LOD levels
  if sound.RollOffMaxDistance then
    sound.RollOffMaxDistance = sound.RollOffMaxDistance * settings.audioQuality
  end

  -- Disable reverb and effects at low LOD
  if lodLevel >= LOD_LEVELS.LOW then
    for _, effect in ipairs(sound:GetChildren()) do
      if effect:IsA("SoundEffect") then
        effect.Enabled = false
      end
    end
  end
end

function LODSystem.applyLODToWeapon(weapon: any, lodLevel: number): ()
  local settings = lodSettings[lodLevel]
  if not settings then
    return
  end

  -- Adjust update frequency for weapon components
  if weapon.updateFrequency then
    weapon.updateFrequency = weapon.updateFrequency * settings.updateFrequency
  end

  -- Reduce physics accuracy at lower LOD levels
  if weapon.physicsAccuracy then
    weapon.physicsAccuracy = weapon.physicsAccuracy * settings.physicsAccuracy
  end

  -- Disable certain features at low LOD
  if lodLevel >= LOD_LEVELS.LOW then
    -- Disable advanced ballistics
    if weapon.advancedBallistics then
      weapon.advancedBallistics = false
    end

    -- Reduce component update rates
    if weapon.componentUpdateRate then
      weapon.componentUpdateRate *= 0.5
    end
  end
end

-- ============================================================================
-- LOD UPDATE SYSTEM
-- ============================================================================

function LODSystem._updateLOD(): ()
  -- Update player position
  LODSystem._updatePlayerPosition()

  -- Update LOD for all registered objects
  local currentTime = os.clock()

  for objectId, lodObject in pairs(lodObjects) do
    if lodObject.isActive then
      -- Calculate distance
      lodObject.distance = LODSystem._calculateDistance(lodObject.position, lodState.playerPosition)

      -- Calculate new LOD level
      local newLOD = LODSystem.calculateLODLevel(lodObject.position, lodObject.type)

      -- Apply LOD if changed
      if newLOD ~= lodObject.currentLOD then
        LODSystem._applyLODToObject(lodObject, newLOD)
        lodObject.currentLOD = newLOD
        lodObject.lastUpdate = currentTime
      end
    end
  end
end

function LODSystem._updatePlayerPosition(): ()
  local player = Players.LocalPlayer
  if player and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
    local position = player.Character.HumanoidRootPart.Position
    lodState.playerPosition = Vector3.new(position.X, position.Y, position.Z)
  end
end

function LODSystem._applyLODToObject(lodObject: any, lodLevel: number): ()
  local objectType = lodObject.type
  local object = lodObject.object

  if objectType == "effect" then
    LODSystem.applyLODToEffect(object, lodLevel)
  elseif objectType == "audio" then
    LODSystem.applyLODToAudio(object, lodLevel)
  elseif objectType == "weapon" then
    LODSystem.applyLODToWeapon(object, lodLevel)
  end
end

function LODSystem._updateAllObjects(): ()
  for objectId, lodObject in pairs(lodObjects) do
    if lodObject.isActive then
      local newLOD = LODSystem.calculateLODLevel(lodObject.position, lodObject.type)
      LODSystem._applyLODToObject(lodObject, newLOD)
      lodObject.currentLOD = newLOD
    end
  end
end

-- ============================================================================
-- PERFORMANCE MONITORING
-- ============================================================================

function LODSystem._monitorPerformance(): ()
  local currentTime = os.clock()

  -- Update performance history every second
  if currentTime - lodState.lastPerformanceCheck >= 1.0 then
    local fps = 1 / RunService.Heartbeat:Wait()

    -- Add to history
    table.insert(lodState.performanceHistory, fps)

    -- Keep only last 30 seconds of data
    if #lodState.performanceHistory > 30 then
      table.remove(lodState.performanceHistory, 1)
    end

    lodState.lastPerformanceCheck = currentTime
  end
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function LODSystem._calculateDistance(pos1: Vector3, pos2: Vector3): number
  local dx = pos1.X - pos2.X
  local dy = pos1.Y - pos2.Y
  local dz = pos1.Z - pos2.Z
  return math.sqrt(dx * dx + dy * dy + dz * dz)
end

function LODSystem.getLODSettings(lodLevel: number): LODSettings?
  return lodSettings[lodLevel]
end

function LODSystem.getCurrentLODLevel(): number
  return lodState.globalLODLevel
end

function LODSystem.getLODStatistics(): { [string]: any }
  local stats = {
    globalLODLevel = lodState.globalLODLevel,
    adaptiveEnabled = lodState.adaptiveEnabled,
    distanceBasedEnabled = lodState.distanceBasedEnabled,
    performanceTarget = lodState.performanceTarget,
    registeredObjects = 0,
    activeObjects = 0,
    lodDistribution = {},
  }

  -- Count objects and LOD distribution
  for level = LOD_LEVELS.ULTRA, LOD_LEVELS.MINIMAL do
    stats.lodDistribution[level] = 0
  end

  for _, lodObject in pairs(lodObjects) do
    stats.registeredObjects += 1
    if lodObject.isActive then
      stats.activeObjects += 1
      stats.lodDistribution[lodObject.currentLOD] += 1
    end
  end

  -- Performance statistics
  if #lodState.performanceHistory > 0 then
    local totalFPS = 0
    for _, fps in ipairs(lodState.performanceHistory) do
      totalFPS += fps
    end
    stats.averageFPS = totalFPS / #lodState.performanceHistory
    stats.performanceRating = stats.averageFPS >= lodState.performanceTarget and "Good" or "Poor"
  end

  return stats
end

function LODSystem.printLODReport(): ()
  local stats = LODSystem.getLODStatistics()

  print("\n🎚️ === LOD SYSTEM REPORT ===")
  print(`Global LOD Level: {lodSettings[stats.globalLODLevel].name}`)
  print(`Adaptive LOD: {stats.adaptiveEnabled and "Enabled" or "Disabled"}`)
  print(`Distance-based LOD: {stats.distanceBasedEnabled and "Enabled" or "Disabled"}`)
  print(`Performance Target: {stats.performanceTarget} FPS`)

  if stats.averageFPS then
    print(`Average FPS: {math.floor(stats.averageFPS)}`)
    print(`Performance: {stats.performanceRating}`)
  end

  print(`\nRegistered Objects: {stats.registeredObjects}`)
  print(`Active Objects: {stats.activeObjects}`)

  print("\nLOD Distribution:")
  for level = LOD_LEVELS.ULTRA, LOD_LEVELS.MINIMAL do
    local count = stats.lodDistribution[level]
    if count > 0 then
      print(`  {lodSettings[level].name}: {count} objects`)
    end
  end

  print("=== END LOD REPORT ===\n")
end

return LODSystem
