--!strict
--[[
	Gun System Framework - Component Registry System

	This module implements the component registry and factory system that allows
	runtime registration and creation of weapon components. It provides:

	- Component factory registration and management
	- Component creation and validation
	- Dependency resolution and conflict detection
	- Component serialization and deserialization
	- Hot-swapping capabilities
]]

local CoreTypes = require(script.Parent.Parent.Types:WaitForChild("GSF_Core"))
local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type ComponentType = Types.ComponentType
type WeaponComponent = Types.WeaponComponent
type ComponentConfig = Types.ComponentConfig
type ComponentStats = Types.ComponentStats
type ValidationResult = Types.ValidationResult
type InitResult = Types.InitResult
type ComponentFactory = Types.ComponentFactory
type ComponentFactoryFunction<T> = Types.ComponentFactoryFunction<T>

-- ============================================================================
-- COMPONENT REGISTRY IMPLEMENTATION
-- ============================================================================

local ComponentRegistry = {}
ComponentRegistry.__index = ComponentRegistry

-- Private storage for registered factories and components
local registeredFactories = {}
local componentBlueprints: { [string]: ComponentConfig } = {}
local componentInstances: { [string]: WeaponComponent } = {}
local dependencyGraph: { [string]: { string } } = {}

-- ============================================================================
-- FACTORY REGISTRATION METHODS
-- ============================================================================

function ComponentRegistry.registerFactory<T>(
  componentType: ComponentType,
  factory: ComponentFactoryFunction<T>
): ()
  assert(componentType, "Component type is required")
  assert(factory, "Factory function is required")
  assert(type(factory) == "function", "Factory must be a function")

  -- Validate component type
  if not CoreTypes.TypeGuards.isValidComponentType(componentType) then
    error(string.format("Invalid component type: %s", componentType))
  end

  -- Check for existing registration
  if registeredFactories[componentType] then
    warn(string.format("Overriding existing factory for component type: %s", componentType))
  end

  -- Register the factory
  registeredFactories[componentType] = factory

  print(
    string.format("[ComponentRegistry] Registered factory for component type: %s", componentType)
  )
end

function ComponentRegistry.unregisterFactory(componentType: ComponentType): boolean
  if not registeredFactories[componentType] then
    warn(string.format("No factory registered for component type: %s", componentType))
    return false
  end

  registeredFactories[componentType] = nil
  print(
    string.format("[ComponentRegistry] Unregistered factory for component type: %s", componentType)
  )
  return true
end

function ComponentRegistry.isFactoryRegistered(componentType: ComponentType): boolean
  return registeredFactories[componentType] ~= nil
end

function ComponentRegistry.getRegisteredFactories(): { ComponentType }
  local factories = {}
  for componentType, _ in pairs(registeredFactories) do
    table.insert(factories, componentType)
  end
  return factories
end

-- ============================================================================
-- COMPONENT CREATION METHODS
-- ============================================================================

function ComponentRegistry.createComponent(
  componentType: ComponentType,
  config: ComponentConfig
): WeaponComponent?
  -- Validate inputs
  if not CoreTypes.TypeGuards.isValidComponentType(componentType) then
    error(string.format("Invalid component type: %s", componentType))
  end

  if not config or type(config) ~= "table" then
    error("Component config is required and must be a table")
  end

  -- Check if factory is registered
  local factory = registeredFactories[componentType]
  if not factory then
    error(string.format("No factory registered for component type: %s", componentType))
  end

  -- Validate configuration
  local validationResult = ComponentRegistry.validateConfig(config)
  if not validationResult.isValid then
    error(string.format("Invalid component configuration: %s", validationResult.description))
  end

  -- Create component using factory
  local success, component = pcall(factory, config)
  if not success then
    error(string.format("Failed to create component: %s", component))
  end

  -- Validate created component
  if not CoreTypes.TypeGuards.isWeaponComponent(component) then
    error("Factory did not return a valid WeaponComponent")
  end

  -- Initialize component
  local initResult = component:initialize(config)
  if not initResult.success then
    error(string.format("Component initialization failed: %s", initResult.message))
  end

  -- Store component instance
  componentInstances[component.id] = component

  print(
    string.format(
      "[ComponentRegistry] Created component: %s (type: %s)",
      component.id,
      componentType
    )
  )
  return component
end

function ComponentRegistry.createFromBlueprint(blueprint: ComponentConfig): WeaponComponent?
  if not blueprint.type then
    error("Blueprint must specify component type")
  end

  return ComponentRegistry.createComponent(blueprint.type, blueprint)
end

function ComponentRegistry.cloneComponent(
  original: WeaponComponent,
  modifications: ComponentConfig?
): WeaponComponent?
  -- Create base config from original component
  local config = table.clone(original.config)

  -- Apply modifications if provided
  if modifications then
    for key, value in pairs(modifications) do
      config[key] = value
    end
  end

  -- Generate new ID for cloned component
  config.id = string.format("%s_clone_%d", original.id, os.clock() * 1000)

  return ComponentRegistry.createComponent(original.type, config)
end

-- ============================================================================
-- VALIDATION METHODS
-- ============================================================================

function ComponentRegistry.validateConfig(config: ComponentConfig): ValidationResult
  local result: ValidationResult = {
    isValid = true,
    severity = "Warning",
    description = "Configuration is valid",
    suggestedAction = "Allow",
  }

  -- Check required fields
  if not config.id or type(config.id) ~= "string" or config.id == "" then
    result.isValid = false
    result.errorCode = "ConfigurationInvalid"
    result.severity = "Error"
    result.description = "Component ID is required and must be a non-empty string"
    result.suggestedAction = "Reject"
    return result
  end

  if not config.name or type(config.name) ~= "string" or config.name == "" then
    result.isValid = false
    result.errorCode = "ConfigurationInvalid"
    result.severity = "Error"
    result.description = "Component name is required and must be a non-empty string"
    result.suggestedAction = "Reject"
    return result
  end

  -- Validate numeric fields
  local numericFields = { "mass", "length", "width", "height", "durability" }
  for _, field in numericFields do
    if config[field] ~= nil then
      if type(config[field]) ~= "number" then
        result.isValid = false
        result.errorCode = "ConfigurationInvalid"
        result.severity = "Error"
        result.description = string.format("Field '%s' must be a number", field)
        result.suggestedAction = "Reject"
        return result
      end

      if config[field] < 0 then
        result.isValid = false
        result.errorCode = "ConfigurationInvalid"
        result.severity = "Error"
        result.description = string.format("Field '%s' cannot be negative", field)
        result.suggestedAction = "Reject"
        return result
      end
    end
  end

  -- Validate durability range
  if config.durability and (config.durability < 0 or config.durability > 1) then
    result.isValid = false
    result.errorCode = "ConfigurationInvalid"
    result.severity = "Error"
    result.description = "Durability must be between 0.0 and 1.0"
    result.suggestedAction = "Reject"
    return result
  end

  return result
end

function ComponentRegistry.validateCompatibility(components: { WeaponComponent }): ValidationResult
  local result: ValidationResult = {
    isValid = true,
    severity = "Warning",
    description = "Components are compatible",
    suggestedAction = "Allow",
  }

  -- Check for conflicts between components
  for i, component1 in ipairs(components) do
    for j, component2 in ipairs(components) do
      if i ~= j then
        -- Check if component1 conflicts with component2
        for _, conflictType in ipairs(component1.conflicts) do
          if component2.type == conflictType then
            result.isValid = false
            result.errorCode = "ComponentIncompatible"
            result.severity = "Error"
            result.description =
              string.format("Component %s conflicts with %s", component1.id, component2.id)
            result.suggestedAction = "Reject"
            return result
          end
        end
      end
    end
  end

  -- Check dependencies
  for _, component in ipairs(components) do
    for _, dependencyType in ipairs(component.dependencies) do
      local dependencyFound = false
      for _, otherComponent in ipairs(components) do
        if otherComponent.type == dependencyType then
          dependencyFound = true
          break
        end
      end

      if not dependencyFound then
        result.isValid = false
        result.errorCode = "DependencyMissing"
        result.severity = "Error"
        result.description = string.format(
          "Component %s requires %s but it's not present",
          component.id,
          dependencyType
        )
        result.suggestedAction = "Reject"
        return result
      end
    end
  end

  return result
end

-- ============================================================================
-- COMPONENT MANAGEMENT METHODS
-- ============================================================================

function ComponentRegistry.getComponent(componentId: string): WeaponComponent?
  return componentInstances[componentId]
end

function ComponentRegistry.getAllComponents(): { WeaponComponent }
  local components = {}
  for _, component in pairs(componentInstances) do
    table.insert(components, component)
  end
  return components
end

function ComponentRegistry.getComponentsByType(componentType: ComponentType): { WeaponComponent }
  local components = {}
  for _, component in pairs(componentInstances) do
    if component.type == componentType then
      table.insert(components, component)
    end
  end
  return components
end

function ComponentRegistry.removeComponent(componentId: string): boolean
  if componentInstances[componentId] then
    componentInstances[componentId] = nil
    print(string.format("[ComponentRegistry] Removed component: %s", componentId))
    return true
  end
  return false
end

-- ============================================================================
-- SERIALIZATION METHODS
-- ============================================================================

function ComponentRegistry.serializeComponent(component: WeaponComponent): { [string]: any }
  return {
    id = component.id,
    type = component.type,
    name = component.name,
    description = component.description,
    version = component.version,
    config = component.config,
    stats = component.stats,
    dependencies = component.dependencies,
    conflicts = component.conflicts,
    customData = component.customData,
  }
end

function ComponentRegistry.deserializeComponent(data: { [string]: any }): WeaponComponent?
  if not data.type or not data.config then
    error("Invalid component data: missing type or config")
  end

  return ComponentRegistry.createComponent(data.type, data.config)
end

-- ============================================================================
-- UTILITY METHODS
-- ============================================================================

function ComponentRegistry.getStats(): { [string]: any }
  return {
    registeredFactories = #ComponentRegistry.getRegisteredFactories(),
    componentInstances = #ComponentRegistry.getAllComponents(),
    memoryUsage = collectgarbage("count"),
  }
end

function ComponentRegistry.cleanup(): ()
  -- Clear all component instances
  for componentId, _ in pairs(componentInstances) do
    componentInstances[componentId] = nil
  end

  print("[ComponentRegistry] Cleanup completed")
end

-- ============================================================================
-- MODULE EXPORT
-- ============================================================================

return ComponentRegistry
