--!strict
--[[
	Gun System Framework - Audio System

	This module manages all audio effects for the weapon system including:
	- Weapon firing sounds with realistic acoustics
	- Suppressor effects
	- Environmental audio reflections
	- 3D spatial positioning
	- Dynamic range and frequency filtering
]]

local RunService = game:GetService("RunService")
local SoundService = game:GetService("SoundService")

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type Weapon = Types.Weapon
type Vector3 = Types.Vector3
type AudioInstance = Types.AudioInstance
type AudioConfiguration = Types.AudioConfiguration
type EnvironmentalAudio = Types.EnvironmentalAudio

-- ============================================================================
-- AUDIO SYSTEM IMPLEMENTATION
-- ============================================================================

local AudioSystem = {}
AudioSystem.__index = AudioSystem

-- Audio configuration
local audioConfig = {
  masterVolume = 1.0,
  weaponVolume = 0.8,
  environmentalVolume = 0.6,
  maxDistance = 1000, -- Maximum audio distance in studs
  rolloffFactor = 0.5,
  dopplerScale = 1.0,
  reverbEnabled = true,
}

-- Sound pools for performance
local soundPools = {
  gunshot = {},
  reload = {},
  mechanical = {},
  impact = {},
}

-- Active audio sources
local activeSources = {}
local audioCounter = 0

-- Environmental audio data
local environmentalData = {
  reverbTime = 1.2,
  dampening = 0.3,
  echoes = true,
  outdoorFactor = 0.8,
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function AudioSystem.initialize(): ()
  print("[AudioSystem] Initializing weapon audio system...")

  -- Configure SoundService
  SoundService.AmbientReverb = Enum.ReverbType.NoReverb
  SoundService.DistanceFactor = 3.33
  SoundService.DopplerScale = audioConfig.dopplerScale
  SoundService.RolloffScale = audioConfig.rolloffFactor

  -- Create sound pools
  AudioSystem._createSoundPools()

  -- Start audio update loop
  RunService.Heartbeat:Connect(AudioSystem._updateAudio)

  print("[AudioSystem] Audio system initialized")
end

function AudioSystem._createSoundPools(): ()
  -- Create gunshot sound pool
  for i = 1, 10 do
    local sound = AudioSystem._createGunshotSound()
    table.insert(soundPools.gunshot, sound)
  end

  -- Create reload sound pool
  for i = 1, 5 do
    local sound = AudioSystem._createReloadSound()
    table.insert(soundPools.reload, sound)
  end

  -- Create mechanical sound pool
  for i = 1, 8 do
    local sound = AudioSystem._createMechanicalSound()
    table.insert(soundPools.mechanical, sound)
  end

  print("[AudioSystem] Created audio pools")
end

-- ============================================================================
-- WEAPON AUDIO EFFECTS
-- ============================================================================

function AudioSystem.playWeaponFire(weapon: Weapon, fireData: any): ()
  local audioData = AudioSystem._calculateFireAudio(weapon, fireData)
  local position = AudioSystem._getWeaponPosition(weapon)

  -- Apply suppressor modifications
  local suppressor = weapon:getComponent("Suppressor")
  if suppressor then
    audioData = suppressor:modifySound(audioData)
  end

  -- Play primary gunshot sound
  AudioSystem._playGunshotSound(audioData, position)

  -- Play mechanical sounds
  AudioSystem._playMechanicalSounds(weapon, fireData, position)

  -- Create environmental effects
  AudioSystem._createEnvironmentalEffects(audioData, position)
end

function AudioSystem.playWeaponReload(weapon: Weapon, reloadData: any): ()
  local position = AudioSystem._getWeaponPosition(weapon)
  local magazine = weapon:getComponent("Magazine")

  -- Play reload sequence sounds
  if reloadData.reloadType == "empty" then
    -- Empty reload: magazine out, new magazine in, bolt release
    AudioSystem._playReloadSequence(weapon, position, {
      "magazine_out",
      "magazine_in",
      "bolt_release",
    })
  else
    -- Tactical reload: magazine out, new magazine in
    AudioSystem._playReloadSequence(weapon, position, {
      "magazine_out",
      "magazine_in",
    })
  end
end

function AudioSystem._createGunshotSound(): Sound
  local sound = Instance.new("Sound")
  sound.Name = "GunshotSound"
  sound.Volume = 0.8
  sound.PlaybackSpeed = 1.0
  sound.RollOffMode = Enum.RollOffMode.InverseTapered
  sound.RollOffMaxDistance = audioConfig.maxDistance
  sound.RollOffMinDistance = 10

  -- Add reverb effect
  local reverb = Instance.new("ReverbSoundEffect")
  reverb.DecayTime = 1.2
  reverb.Density = 0.8
  reverb.Diffusion = 0.9
  reverb.DryLevel = -6
  reverb.WetLevel = -10
  reverb.Parent = sound

  -- Add EQ for realistic frequency response
  local eq = Instance.new("EqualizerSoundEffect")
  eq.HighGain = -3
  eq.MidGain = 2
  eq.LowGain = 1
  eq.Parent = sound

  return sound
end

function AudioSystem._createReloadSound(): Sound
  local sound = Instance.new("Sound")
  sound.Name = "ReloadSound"
  sound.Volume = 0.4
  sound.PlaybackSpeed = 1.0
  sound.RollOffMode = Enum.RollOffMode.Linear
  sound.RollOffMaxDistance = 50
  sound.RollOffMinDistance = 2

  return sound
end

function AudioSystem._createMechanicalSound(): Sound
  local sound = Instance.new("Sound")
  sound.Name = "MechanicalSound"
  sound.Volume = 0.3
  sound.PlaybackSpeed = 1.0
  sound.RollOffMode = Enum.RollOffMode.Linear
  sound.RollOffMaxDistance = 30
  sound.RollOffMinDistance = 1

  return sound
end

function AudioSystem._playGunshotSound(audioData: AudioInstance, position: Vector3): ()
  local sound = AudioSystem._getPooledSound("gunshot")
  if not sound then
    return
  end

  -- Configure sound properties
  sound.Volume = audioData.volume * audioConfig.weaponVolume * audioConfig.masterVolume
  sound.PlaybackSpeed = audioData.pitch
  sound.SoundId = audioData.soundId or "rbxasset://sounds/impact_water.mp3" -- Placeholder

  -- Position the sound
  local soundPart = AudioSystem._createSoundPart(position)
  sound.Parent = soundPart

  -- Apply frequency filtering based on distance and environment
  AudioSystem._applyEnvironmentalFiltering(sound, position)

  -- Play sound
  sound:Play()

  -- Track active sound
  audioCounter += 1
  activeSources[audioCounter] = {
    sound = sound,
    part = soundPart,
    startTime = os.clock(),
    duration = sound.TimeLength,
  }

  -- Clean up when finished
  sound.Ended:Connect(function()
    AudioSystem._returnSoundToPool(sound, "gunshot")
    if soundPart then
      soundPart:Destroy()
    end
    activeSources[audioCounter] = nil
  end)
end

function AudioSystem._playMechanicalSounds(weapon: Weapon, fireData: any, position: Vector3): ()
  -- Play bolt/action sound
  local mechanicalSound = AudioSystem._getPooledSound("mechanical")
  if mechanicalSound then
    mechanicalSound.Volume = 0.2 * audioConfig.weaponVolume * audioConfig.masterVolume
    mechanicalSound.PlaybackSpeed = 0.9 + math.random() * 0.2
    mechanicalSound.SoundId = "rbxasset://sounds/button.wav" -- Placeholder

    local soundPart = AudioSystem._createSoundPart(position)
    mechanicalSound.Parent = soundPart

    -- Delay mechanical sound slightly
    wait(0.05)
    mechanicalSound:Play()

    mechanicalSound.Ended:Connect(function()
      AudioSystem._returnSoundToPool(mechanicalSound, "mechanical")
      if soundPart then
        soundPart:Destroy()
      end
    end)
  end
end

function AudioSystem._playReloadSequence(
  weapon: Weapon,
  position: Vector3,
  sequence: { string }
): ()
  local delays = {
    magazine_out = 0.0,
    magazine_in = 0.8,
    bolt_release = 1.6,
  }

  for _, action in ipairs(sequence) do
    local delay = delays[action] or 0

    task.wait(delay)

    local sound = AudioSystem._getPooledSound("reload")
    if sound then
      sound.Volume = 0.3 * audioConfig.weaponVolume * audioConfig.masterVolume
      sound.PlaybackSpeed = 0.95 + math.random() * 0.1
      sound.SoundId = AudioSystem._getReloadSoundId(action)

      local soundPart = AudioSystem._createSoundPart(position)
      sound.Parent = soundPart
      sound:Play()

      sound.Ended:Connect(function()
        AudioSystem._returnSoundToPool(sound, "reload")
        if soundPart then
          soundPart:Destroy()
        end
      end)
    end
  end
end

-- ============================================================================
-- ENVIRONMENTAL AUDIO EFFECTS
-- ============================================================================

function AudioSystem._createEnvironmentalEffects(audioData: AudioInstance, position: Vector3): ()
  -- Create echo/reverb effects based on environment
  if environmentalData.echoes then
    AudioSystem._createEchoEffect(audioData, position)
  end

  -- Create distant sound effect
  AudioSystem._createDistantSoundEffect(audioData, position)
end

function AudioSystem._createEchoEffect(audioData: AudioInstance, position: Vector3): ()
  -- Simple echo implementation
  task.wait(0.3) -- Echo delay

  local echoSound = AudioSystem._getPooledSound("gunshot")
  if echoSound then
    echoSound.Volume = audioData.volume * 0.3 * audioConfig.environmentalVolume
    echoSound.PlaybackSpeed = audioData.pitch * 0.95
    echoSound.SoundId = audioData.soundId or "rbxasset://sounds/impact_water.mp3"

    -- Position echo slightly offset
    local echoPosition = {
      X = position.X + math.random(-50, 50),
      Y = position.Y + math.random(-20, 20),
      Z = position.Z + math.random(-50, 50),
    }

    local soundPart = AudioSystem._createSoundPart(echoPosition)
    echoSound.Parent = soundPart
    echoSound:Play()

    echoSound.Ended:Connect(function()
      AudioSystem._returnSoundToPool(echoSound, "gunshot")
      if soundPart then
        soundPart:Destroy()
      end
    end)
  end
end

function AudioSystem._createDistantSoundEffect(audioData: AudioInstance, position: Vector3): ()
  -- Create a low-frequency distant sound for far-away listeners
  task.wait(0.1)

  local distantSound = AudioSystem._getPooledSound("gunshot")
  if distantSound then
    distantSound.Volume = audioData.volume * 0.5 * audioConfig.environmentalVolume
    distantSound.PlaybackSpeed = audioData.pitch * 0.8
    distantSound.SoundId = audioData.soundId or "rbxasset://sounds/impact_water.mp3"

    -- Apply low-pass filtering for distance
    local lowPass = Instance.new("EqualizerSoundEffect")
    lowPass.Cutoff = 800 -- Hz
    lowPass.Q = 0.7
    lowPass.Parent = distantSound

    local soundPart = AudioSystem._createSoundPart(position)
    distantSound.Parent = soundPart
    distantSound:Play()

    distantSound.Ended:Connect(function()
      AudioSystem._returnSoundToPool(distantSound, "gunshot")
      if soundPart then
        soundPart:Destroy()
      end
    end)
  end
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function AudioSystem._calculateFireAudio(weapon: Weapon, fireData: any): AudioInstance
  local barrel = weapon:getComponent("Barrel")
  local baseVolume = 1.0
  local basePitch = 1.0

  -- Barrel length affects sound characteristics
  if barrel then
    -- Longer barrels = slightly lower pitch, higher volume
    basePitch = math.max(0.8, 1.0 - (barrel.length - 16) * 0.01)
    baseVolume = math.min(1.2, 1.0 + (barrel.length - 16) * 0.005)
  end

  return {
    volume = baseVolume,
    pitch = basePitch,
    soundId = AudioSystem._getWeaponSoundId(weapon),
  }
end

function AudioSystem._getWeaponSoundId(weapon: Weapon): string
  -- Return appropriate sound ID based on weapon type and caliber
  if weapon.category == "AssaultRifle" then
    return "rbxasset://sounds/impact_water.mp3" -- Placeholder
  elseif weapon.category == "SniperRifle" then
    return "rbxasset://sounds/impact_water.mp3" -- Placeholder
  else
    return "rbxasset://sounds/impact_water.mp3" -- Placeholder
  end
end

function AudioSystem._getReloadSoundId(action: string): string
  local soundIds = {
    magazine_out = "rbxasset://sounds/button.wav",
    magazine_in = "rbxasset://sounds/button.wav",
    bolt_release = "rbxasset://sounds/button.wav",
  }

  return soundIds[action] or "rbxasset://sounds/button.wav"
end

function AudioSystem._getPooledSound(soundType: string): Sound?
  local pool = soundPools[soundType]
  if pool and #pool > 0 then
    return table.remove(pool, #pool)
  end

  -- Create new sound if pool is empty
  if soundType == "gunshot" then
    return AudioSystem._createGunshotSound()
  elseif soundType == "reload" then
    return AudioSystem._createReloadSound()
  elseif soundType == "mechanical" then
    return AudioSystem._createMechanicalSound()
  end

  return nil
end

function AudioSystem._returnSoundToPool(sound: Sound, soundType: string): ()
  sound:Stop()
  sound.Parent = nil

  -- Clear any temporary effects
  for _, effect in ipairs(sound:GetChildren()) do
    if effect:IsA("SoundEffect") and effect.Name:find("Temp") then
      effect:Destroy()
    end
  end

  table.insert(soundPools[soundType], sound)
end

function AudioSystem._createSoundPart(position: Vector3): Part
  local part = Instance.new("Part")
  part.Name = "SoundSource"
  part.Size = Vector3.new(0.1, 0.1, 0.1)
  part.Position = Vector3.new(position.X, position.Y, position.Z)
  part.Anchored = true
  part.CanCollide = false
  part.Transparency = 1
  part.Parent = workspace

  return part
end

function AudioSystem._getWeaponPosition(weapon: Weapon): Vector3
  if weapon.instance then
    return weapon.instance.Position
  end

  return { X = 0, Y = 0, Z = 0 }
end

function AudioSystem._applyEnvironmentalFiltering(sound: Sound, position: Vector3): ()
  -- Apply environmental audio filtering based on position and surroundings
  -- This is a simplified implementation

  local player = game.Players.LocalPlayer
  if player and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
    local playerPosition = player.Character.HumanoidRootPart.Position
    local distance = (Vector3.new(position.X, position.Y, position.Z) - playerPosition).Magnitude

    -- Apply distance-based filtering
    if distance > 100 then
      local lowPass = Instance.new("EqualizerSoundEffect")
      lowPass.Name = "TempDistanceFilter"
      lowPass.Cutoff = math.max(400, 2000 - distance * 2)
      lowPass.Q = 0.5
      lowPass.Parent = sound
    end
  end
end

function AudioSystem._updateAudio(): ()
  -- Update active audio sources and clean up expired ones
  local currentTime = os.clock()
  local toRemove = {}

  for audioId, audioData in pairs(activeSources) do
    if currentTime - audioData.startTime >= audioData.duration + 1 then
      table.insert(toRemove, audioId)
    end
  end

  for _, audioId in ipairs(toRemove) do
    activeSources[audioId] = nil
  end
end

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

function AudioSystem.setMasterVolume(volume: number): ()
  audioConfig.masterVolume = math.max(0, math.min(1, volume))
end

function AudioSystem.setWeaponVolume(volume: number): ()
  audioConfig.weaponVolume = math.max(0, math.min(1, volume))
end

function AudioSystem.setEnvironmentalVolume(volume: number): ()
  audioConfig.environmentalVolume = math.max(0, math.min(1, volume))
end

return AudioSystem
