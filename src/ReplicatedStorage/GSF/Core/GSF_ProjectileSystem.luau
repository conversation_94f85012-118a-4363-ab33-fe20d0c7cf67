--!strict
--[[
	Gun System Framework - Projectile Physics System

	This module implements the core projectile physics system including:
	- Projectile creation and lifecycle management
	- Physics simulation with gravity and air resistance
	- Collision detection using Roblox raycasting
	- Penetration and ricochet mechanics
	- Performance optimization and cleanup
]]

local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")

local AdvancedBallistics = require(script.Parent.GSF_Ballistics)
local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type Projectile = Types.Projectile
type ProjectileConfig = Types.ProjectileConfig
type ProjectileSystem = Types.ProjectileSystem
type CollisionResult = Types.CollisionResult
type PenetrationResult = Types.PenetrationResult
type RicochetResult = Types.RicochetResult
type TrajectoryUpdate = Types.TrajectoryUpdate
type EnvironmentalFactors = Types.EnvironmentalFactors
type MaterialProperties = Types.MaterialProperties
type Vector3 = Types.Vector3
type Ray = Types.Ray
type AmmunitionType = Types.AmmunitionType
type MaterialType = Types.MaterialType

-- ============================================================================
-- PROJECTILE SYSTEM IMPLEMENTATION
-- ============================================================================

local ProjectileSystemImpl = {}
ProjectileSystemImpl.__index = ProjectileSystemImpl

-- Active projectiles storage
local activeProjectiles: { [string]: Projectile } = {}
local projectileCounter = 0

-- Performance tracking
local performanceStats = {
  activeProjectileCount = 0,
  totalProjectilesCreated = 0,
  totalProjectilesDestroyed = 0,
  averageUpdateTime = 0,
  lastUpdateTime = 0,
}

-- Environmental defaults
local defaultEnvironment: EnvironmentalFactors = {
  temperature = 20, -- Celsius
  pressure = 101.325, -- kPa
  humidity = 0.5,
  windVelocity = { X = 0, Y = 0, Z = 0 },
  windGustFactor = 0.1,
  gravity = { X = 0, Y = -9.81, Z = 0 }, -- m/s²
  airDensity = 1.225, -- kg/m³
  visibility = 1.0,
  lightLevel = 1.0,
}

-- Material properties lookup
local materialProperties: { [MaterialType]: MaterialProperties } = {
  Steel = {
    name = "Steel",
    density = 7850,
    hardness = 0.8,
    elasticity = 0.3,
    friction = 0.6,
    penetrationResistance = 0.9,
    spallationFactor = 0.3,
    ricochetThreshold = 15,
    ricochetCoefficient = 0.7,
  },
  Aluminum = {
    name = "Aluminum",
    density = 2700,
    hardness = 0.4,
    elasticity = 0.4,
    friction = 0.5,
    penetrationResistance = 0.4,
    spallationFactor = 0.2,
    ricochetThreshold = 20,
    ricochetCoefficient = 0.8,
  },
  Concrete = {
    name = "Concrete",
    density = 2400,
    hardness = 0.7,
    elasticity = 0.1,
    friction = 0.8,
    penetrationResistance = 0.6,
    spallationFactor = 0.5,
    ricochetThreshold = 10,
    ricochetCoefficient = 0.3,
  },
  Wood = {
    name = "Wood",
    density = 600,
    hardness = 0.2,
    elasticity = 0.3,
    friction = 0.7,
    penetrationResistance = 0.2,
    spallationFactor = 0.1,
    ricochetThreshold = 30,
    ricochetCoefficient = 0.5,
  },
  Glass = {
    name = "Glass",
    density = 2500,
    hardness = 0.6,
    elasticity = 0.1,
    friction = 0.3,
    penetrationResistance = 0.1,
    spallationFactor = 0.8,
    ricochetThreshold = 5,
    ricochetCoefficient = 0.2,
  },
}

-- ============================================================================
-- PROJECTILE CREATION AND MANAGEMENT
-- ============================================================================

function ProjectileSystemImpl.createProjectile(
  config: ProjectileConfig,
  origin: Vector3,
  velocity: Vector3
): Projectile
  projectileCounter += 1
  local projectileId = `projectile_{projectileCounter}_{tick()}`

  local projectile: Projectile = {
    -- Identity
    id = projectileId,
    ammunition = config.ammunition,
    sourceWeaponId = "",
    ownerId = nil,

    -- Physics properties
    position = origin,
    velocity = velocity,
    mass = config.ballistics.mass,
    diameter = config.ballistics.diameter,
    coefficientOfDrag = config.ballistics.coefficientOfDrag,
    ballisticCoefficient = config.ballistics.ballisticCoefficient,

    -- State tracking
    energy = ProjectileSystemImpl._calculateKineticEnergy(
      config.ballistics.mass,
      math.sqrt(velocity.X ^ 2 + velocity.Y ^ 2 + velocity.Z ^ 2)
    ),
    spin = 0, -- Would be calculated from barrel twist
    timeAlive = 0,
    distanceTraveled = 0,
    hasRicocheted = false,
    penetrationCount = 0,

    -- Damage properties
    damage = config.damage,
    penetrationPower = config.ballistics.penetrationPower,
    fragmentationChance = config.ballistics.fragmentationChance,

    -- Environmental tracking
    lastPosition = origin,
    trajectory = { origin },

    -- Lifecycle flags
    isActive = true,
    shouldDestroy = false,
    destroyReason = nil,
  }

  -- Store projectile
  activeProjectiles[projectileId] = projectile
  performanceStats.activeProjectileCount += 1
  performanceStats.totalProjectilesCreated += 1

  return projectile
end

function ProjectileSystemImpl.destroyProjectile(projectile: Projectile, reason: string): ()
  if activeProjectiles[projectile.id] then
    projectile.isActive = false
    projectile.shouldDestroy = true
    projectile.destroyReason = reason

    activeProjectiles[projectile.id] = nil
    performanceStats.activeProjectileCount -= 1
    performanceStats.totalProjectilesDestroyed += 1
  end
end

function ProjectileSystemImpl.getActiveProjectiles(): { Projectile }
  local projectiles = {}
  for _, projectile in pairs(activeProjectiles) do
    table.insert(projectiles, projectile)
  end
  return projectiles
end

function ProjectileSystemImpl.getProjectileCount(): number
  return performanceStats.activeProjectileCount
end

-- ============================================================================
-- PHYSICS SIMULATION
-- ============================================================================

function ProjectileSystemImpl.updateProjectile(
  projectile: Projectile,
  deltaTime: number
): TrajectoryUpdate
  if not projectile.isActive then
    return {
      newPosition = projectile.position,
      newVelocity = projectile.velocity,
      energyLoss = 0,
      collisions = {},
      shouldContinue = false,
    }
  end

  -- Store last position
  projectile.lastPosition = projectile.position

  -- Apply physics forces
  local gravityForce = ProjectileSystemImpl.applyGravity(projectile, deltaTime)
  local dragForce = ProjectileSystemImpl.applyAirResistance(projectile, defaultEnvironment)

  -- Update velocity
  local newVelocity = {
    X = projectile.velocity.X + (gravityForce.X + dragForce.X) * deltaTime,
    Y = projectile.velocity.Y + (gravityForce.Y + dragForce.Y) * deltaTime,
    Z = projectile.velocity.Z + (gravityForce.Z + dragForce.Z) * deltaTime,
  }

  -- Calculate new position
  local newPosition = {
    X = projectile.position.X + newVelocity.X * deltaTime,
    Y = projectile.position.Y + newVelocity.Y * deltaTime,
    Z = projectile.position.Z + newVelocity.Z * deltaTime,
  }

  -- Calculate distance traveled
  local distance = math.sqrt(
    (newPosition.X - projectile.position.X) ^ 2
      + (newPosition.Y - projectile.position.Y) ^ 2
      + (newPosition.Z - projectile.position.Z) ^ 2
  )
  projectile.distanceTraveled += distance

  -- Update projectile state
  projectile.timeAlive += deltaTime

  -- Check for collisions along trajectory
  local trajectory: Ray = {
    Origin = projectile.position,
    Direction = {
      X = newPosition.X - projectile.position.X,
      Y = newPosition.Y - projectile.position.Y,
      Z = newPosition.Z - projectile.position.Z,
    },
  }

  local collisions = ProjectileSystemImpl.checkCollisions(projectile, trajectory)

  -- Calculate energy loss
  local velocityMagnitude = math.sqrt(newVelocity.X ^ 2 + newVelocity.Y ^ 2 + newVelocity.Z ^ 2)
  local newEnergy = ProjectileSystemImpl._calculateKineticEnergy(projectile.mass, velocityMagnitude)
  local energyLoss = projectile.energy - newEnergy

  -- Update projectile properties
  projectile.position = newPosition
  projectile.velocity = newVelocity
  projectile.energy = newEnergy

  -- Add to trajectory history
  table.insert(projectile.trajectory, newPosition)

  -- Check destruction conditions
  local shouldContinue = ProjectileSystemImpl._checkDestructionConditions(projectile)

  return {
    newPosition = newPosition,
    newVelocity = newVelocity,
    energyLoss = energyLoss,
    collisions = collisions,
    shouldContinue = shouldContinue,
  }
end

-- ============================================================================
-- COLLISION DETECTION
-- ============================================================================

function ProjectileSystemImpl.checkCollisions(
  projectile: Projectile,
  trajectory: Ray
): { CollisionResult }
  local collisions = {}

  -- Create Roblox raycast params
  local raycastParams = RaycastParams.new()
  raycastParams.FilterType = Enum.RaycastFilterType.Exclude
  raycastParams.FilterDescendantsInstances = {} -- Could filter out certain objects

  -- Convert our ray to Roblox format
  local origin = Vector3.new(trajectory.Origin.X, trajectory.Origin.Y, trajectory.Origin.Z)
  local direction =
    Vector3.new(trajectory.Direction.X, trajectory.Direction.Y, trajectory.Direction.Z)

  -- Perform raycast
  local raycastResult = Workspace:Raycast(origin, direction, raycastParams)

  if raycastResult then
    local collision: CollisionResult = {
      hit = true,
      position = {
        X = raycastResult.Position.X,
        Y = raycastResult.Position.Y,
        Z = raycastResult.Position.Z,
      },
      normal = {
        X = raycastResult.Normal.X,
        Y = raycastResult.Normal.Y,
        Z = raycastResult.Normal.Z,
      },
      distance = raycastResult.Distance,
      instance = raycastResult.Instance,
      material = ProjectileSystemImpl._convertRobloxMaterial(raycastResult.Material),
      thickness = ProjectileSystemImpl._estimateThickness(raycastResult.Instance),
      hitPlayer = nil, -- Would be determined by checking if instance belongs to a player
      hitBodyPart = nil, -- Would be determined by checking instance name/type
      impactAngle = ProjectileSystemImpl._calculateImpactAngle(
        trajectory.Direction,
        raycastResult.Normal
      ),
      impactVelocity = math.sqrt(
        projectile.velocity.X ^ 2 + projectile.velocity.Y ^ 2 + projectile.velocity.Z ^ 2
      ),
      impactEnergy = projectile.energy,
    }

    table.insert(collisions, collision)
  end

  return collisions
end

function ProjectileSystemImpl.calculatePenetration(
  projectile: Projectile,
  collision: CollisionResult
): PenetrationResult
  local material = materialProperties[collision.material]
  if not material then
    -- Default material properties
    material = materialProperties.Steel
  end

  -- Simplified penetration calculation
  local penetrationPower = projectile.penetrationPower
  local resistance = material.penetrationResistance * (collision.thickness or 1)
  local angleModifier = math.cos(math.rad(collision.impactAngle))

  local effectivePenetration = penetrationPower * angleModifier
  local canPenetrate = effectivePenetration > resistance

  local energyLoss = math.min(projectile.energy * 0.3, resistance * 1000) -- Simplified energy loss
  local velocityLoss = math.sqrt(2 * energyLoss / projectile.mass)

  return {
    canPenetrate = canPenetrate,
    penetrationDepth = canPenetrate and (collision.thickness or 0)
      or effectivePenetration / resistance,
    energyLoss = energyLoss,
    velocityLoss = velocityLoss,
    deflectionAngle = canPenetrate and 0 or math.random(5, 15),
    exitVelocity = canPenetrate and {
      X = projectile.velocity.X * 0.8,
      Y = projectile.velocity.Y * 0.8,
      Z = projectile.velocity.Z * 0.8,
    } or nil,
    exitPosition = canPenetrate and collision.position or nil,
    fragmentationOccurred = math.random() < projectile.fragmentationChance,
    fragments = {}, -- Would be calculated if fragmentation occurred
  }
end

function ProjectileSystemImpl.calculateRicochet(
  projectile: Projectile,
  collision: CollisionResult
): RicochetResult
  local material = materialProperties[collision.material]
  if not material then
    material = materialProperties.Steel
  end

  local willRicochet = collision.impactAngle < material.ricochetThreshold

  if not willRicochet then
    return {
      willRicochet = false,
      newDirection = nil,
      energyRetained = 0,
      velocityRetained = 0,
      ricochetAngle = 0,
      surfaceFriction = material.friction,
      soundIntensity = 0,
    }
  end

  -- Calculate ricochet direction (simplified)
  local normal = collision.normal
  local incomingDirection = projectile.velocity

  -- Reflect velocity vector
  local dotProduct = incomingDirection.X * normal.X
    + incomingDirection.Y * normal.Y
    + incomingDirection.Z * normal.Z
  local newDirection = {
    X = incomingDirection.X - 2 * dotProduct * normal.X,
    Y = incomingDirection.Y - 2 * dotProduct * normal.Y,
    Z = incomingDirection.Z - 2 * dotProduct * normal.Z,
  }

  local energyRetained = material.ricochetCoefficient * (1 - material.friction)
  local velocityRetained = math.sqrt(energyRetained)

  return {
    willRicochet = true,
    newDirection = newDirection,
    energyRetained = energyRetained,
    velocityRetained = velocityRetained,
    ricochetAngle = collision.impactAngle,
    surfaceFriction = material.friction,
    soundIntensity = 0.5,
  }
end

-- ============================================================================
-- PHYSICS FORCES
-- ============================================================================

function ProjectileSystemImpl.applyGravity(projectile: Projectile, deltaTime: number): Vector3
  return {
    X = defaultEnvironment.gravity.X,
    Y = defaultEnvironment.gravity.Y,
    Z = defaultEnvironment.gravity.Z,
  }
end

function ProjectileSystemImpl.applyAirResistance(
  projectile: Projectile,
  environment: EnvironmentalFactors
): Vector3
  local velocity = projectile.velocity
  local velocityMagnitude = math.sqrt(velocity.X ^ 2 + velocity.Y ^ 2 + velocity.Z ^ 2)

  if velocityMagnitude == 0 then
    return { X = 0, Y = 0, Z = 0 }
  end

  -- Simplified drag calculation: F = 0.5 * ρ * v² * Cd * A
  local dragCoefficient = projectile.coefficientOfDrag
  local area = math.pi * (projectile.diameter / 2000) ^ 2 -- Convert mm to m
  local dragMagnitude = 0.5
    * environment.airDensity
    * velocityMagnitude ^ 2
    * dragCoefficient
    * area

  -- Apply drag opposite to velocity direction
  local dragForce = {
    X = -(velocity.X / velocityMagnitude) * dragMagnitude / projectile.mass * 1000, -- Convert to m/s²
    Y = -(velocity.Y / velocityMagnitude) * dragMagnitude / projectile.mass * 1000,
    Z = -(velocity.Z / velocityMagnitude) * dragMagnitude / projectile.mass * 1000,
  }

  return dragForce
end

function ProjectileSystemImpl.applyMagnusEffect(projectile: Projectile): Vector3
  -- Simplified Magnus effect (would require spin rate)
  return { X = 0, Y = 0, Z = 0 }
end

function ProjectileSystemImpl.applyWindEffect(
  projectile: Projectile,
  environment: EnvironmentalFactors
): Vector3
  -- Simplified wind effect
  local windForce = 0.1 -- Simplified wind force coefficient
  return {
    X = environment.windVelocity.X * windForce,
    Y = environment.windVelocity.Y * windForce,
    Z = environment.windVelocity.Z * windForce,
  }
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function ProjectileSystemImpl._calculateKineticEnergy(mass: number, velocity: number): number
  return 0.5 * (mass / 1000) * velocity ^ 2 -- Convert mass from grams to kg
end

function ProjectileSystemImpl._convertRobloxMaterial(material: Enum.Material): MaterialType
  -- Convert Roblox material to our material type
  if material == Enum.Material.Metal then
    return "Steel"
  elseif material == Enum.Material.Wood then
    return "Wood"
  elseif material == Enum.Material.Concrete then
    return "Concrete"
  elseif material == Enum.Material.Glass then
    return "Glass"
  else
    return "Steel" -- Default
  end
end

function ProjectileSystemImpl._estimateThickness(instance: any): number
  -- Simplified thickness estimation based on part size
  if instance and instance:IsA("BasePart") then
    local size = instance.Size
    return math.min(size.X, size.Y, size.Z) -- Use smallest dimension
  end
  return 0.1 -- Default thickness
end

function ProjectileSystemImpl._calculateImpactAngle(direction: Vector3, normal: Vector3): number
  -- Calculate angle between direction and normal
  local dotProduct = direction.X * normal.X + direction.Y * normal.Y + direction.Z * normal.Z
  local dirMagnitude = math.sqrt(direction.X ^ 2 + direction.Y ^ 2 + direction.Z ^ 2)
  local normalMagnitude = math.sqrt(normal.X ^ 2 + normal.Y ^ 2 + normal.Z ^ 2)

  local cosAngle = dotProduct / (dirMagnitude * normalMagnitude)
  return math.deg(math.acos(math.abs(cosAngle)))
end

function ProjectileSystemImpl._checkDestructionConditions(projectile: Projectile): boolean
  -- Check various destruction conditions
  if projectile.timeAlive > 10 then -- 10 second max lifetime
    ProjectileSystemImpl.destroyProjectile(projectile, "Lifetime exceeded")
    return false
  end

  if projectile.distanceTraveled > 2000 then -- 2000 stud max range
    ProjectileSystemImpl.destroyProjectile(projectile, "Maximum range exceeded")
    return false
  end

  local velocityMagnitude =
    math.sqrt(projectile.velocity.X ^ 2 + projectile.velocity.Y ^ 2 + projectile.velocity.Z ^ 2)
  if velocityMagnitude < 50 then -- 50 m/s minimum velocity
    ProjectileSystemImpl.destroyProjectile(projectile, "Velocity too low")
    return false
  end

  return true
end

function ProjectileSystemImpl.cleanupExpiredProjectiles(): number
  local cleanedCount = 0
  for projectileId, projectile in pairs(activeProjectiles) do
    if projectile.shouldDestroy or not projectile.isActive then
      activeProjectiles[projectileId] = nil
      cleanedCount += 1
    end
  end

  performanceStats.activeProjectileCount = #ProjectileSystemImpl.getActiveProjectiles()
  return cleanedCount
end

-- ============================================================================
-- SYSTEM INITIALIZATION
-- ============================================================================

function ProjectileSystemImpl.initialize(): ()
  print("[ProjectileSystem] Initializing projectile physics system...")

  -- Initialize advanced ballistics
  AdvancedBallistics.initialize()

  -- Set up update loop
  RunService.Heartbeat:Connect(function(deltaTime)
    local startTime = os.clock()

    -- Update all active projectiles
    for _, projectile in pairs(activeProjectiles) do
      ProjectileSystemImpl.updateProjectile(projectile, deltaTime)
    end

    -- Cleanup expired projectiles
    ProjectileSystemImpl.cleanupExpiredProjectiles()

    -- Update performance stats
    local updateTime = os.clock() - startTime
    performanceStats.lastUpdateTime = updateTime
    performanceStats.averageUpdateTime = (performanceStats.averageUpdateTime + updateTime) / 2
  end)

  print("[ProjectileSystem] Projectile system initialized")
end

function ProjectileSystemImpl.getStats(): { [string]: any }
  return performanceStats
end

-- ============================================================================
-- MODULE EXPORT
-- ============================================================================

return ProjectileSystemImpl
