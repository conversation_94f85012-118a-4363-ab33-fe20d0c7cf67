--!strict
--[[
	Gun System Framework - Centralized Configuration System

	This module provides a centralized configuration system for all GSF features:
	- Enable/disable any system or feature
	- Customize behavior parameters
	- Runtime configuration changes
	- Profile-based configurations
	- Performance optimization presets
]]

local ConfigurationSystem = {}
ConfigurationSystem.__index = ConfigurationSystem

-- ============================================================================
-- DEFAULT CONFIGURATION
-- ============================================================================

local defaultConfig = {
	-- Core Systems
	core = {
		enableWeaponSystem = true,
		enableComponentSystem = true,
		enableEventSystem = true,
		enableProjectileSystem = true,
	},

	-- Weapon Features
	weapon = {
		enableRecoil = true,
		enableSpread = true,
		enableDamageDropoff = true,
		enablePenetration = true,
		enableRicochet = false,
		enableJamming = false, -- Disabled as requested
		enableOverheating = false, -- Disabled as requested
		enableDegradation = false, -- Can be enabled if wanted
		enableMalfunctions = false,
	},

	-- Component Features
	components = {
		enableHotSwapping = true,
		enableDegradation = false, -- Disabled by default
		enableCompatibilityChecking = true,
		enableStatModification = true,
		enableVisualChanges = true,
	},

	-- Animation System
	animations = {
		enableCFrameAnimations = true,
		enableRecoilAnimation = true,
		enableSwayAnimation = true,
		enableReloadAnimation = true,
		enableInspectionAnimation = true,
		enableProceduralAnimations = true,

		-- Animation intensities
		recoilIntensity = 1.0,
		swayIntensity = 1.0,
		animationSmoothness = 0.1,
	},

	-- Ballistics System
	ballistics = {
		enableAdvancedBallistics = true,
		enableEnvironmentalEffects = true,
		enableWindEffects = true,
		enableGravity = true,
		enableDragCalculation = true,
		enableSpinDrift = false,
		enableCoriolisEffect = false,

		-- Ballistics accuracy
		ballisticsAccuracy = "High", -- Low, Medium, High, Ultra
		trajectorySteps = 100,
	},

	-- Effects System
	effects = {
		enableMuzzleFlash = true,
		enableShellEjection = true,
		enableImpactEffects = true,
		enableBloodEffects = false,
		enableSmokeTrails = true,
		enableParticleEffects = true,

		-- Effect quality
		effectQuality = "High", -- Low, Medium, High, Ultra
		maxActiveEffects = 100,
		effectLifetime = 5.0,
	},

	-- Audio System
	audio = {
		enableWeaponAudio = true,
		enableSpatialAudio = true,
		enableEnvironmentalAudio = true,
		enableSuppressionEffects = true,
		enableDistanceFiltering = true,
		enableReverb = true,

		-- Audio settings
		masterVolume = 1.0,
		weaponVolume = 1.0,
		environmentalVolume = 0.7,
		maxAudioDistance = 1000,
	},

	-- Performance System
	performance = {
		enablePerformanceMonitoring = true,
		enableLODSystem = true,
		enableObjectPooling = true,
		enableAutomaticOptimization = true,
		enablePerformanceAlerts = true,

		-- Performance targets
		targetFPS = 60,
		memoryThreshold = 100, -- MB
		maxActiveProjectiles = 200,
		maxActiveEffects = 100,
	},

	-- UI System
	ui = {
		enableWeaponCustomizationUI = true,
		enableRealTimeStats = true,
		enableComponentPreview = true,
		enable3DInspection = true,
		enablePerformanceUI = false,

		-- UI settings
		uiScale = 1.0,
		animationSpeed = 1.0,
	},

	-- Viewmodel System (FPS)
	viewmodel = {
		enabled = true,
		armsType = "16", -- "R6", "R15", "Blocky"
		fieldOfView = 70,
		adsFieldOfView = 50,
		mouseSensitivity = 1.0,
		hideCharacterInFirstPerson = true,

		-- Arms settings
		armsTransparency = 0,
		enableArmsAnimations = true,

		-- Weapon positioning
		weaponPosition = { X = 0.5, Y = -0.5, Z = -2 },
		weaponRotation = { X = 0, Y = 0, Z = 0 },

		-- Camera effects
		enableRecoil = true,
		enableSway = true,
		enableBobbing = true,
		bobbingIntensity = 0.02,
		swayIntensity = 0.01,
		recoilDecayRate = 5,

		-- ADS settings
		adsTransitionTime = 0.3,
		adsSensitivityMultiplier = 0.5,
	},

	-- Network System
	network = {
		enableClientPrediction = true,
		enableServerValidation = true,
		enableAntiCheat = true,
		enableNetworkOptimization = true,

		-- Network settings
		updateRate = 20, -- Hz
		compressionLevel = "Medium",
	},

	-- Debug System
	debug = {
		enableDebugMode = false,
		enableVerboseLogging = false,
		enablePerformanceLogging = false,
		enableTrajectoryVisualization = false,
		enableHitboxVisualization = false,
		enableComponentDebugInfo = false,
	},
}

-- Current active configuration
local activeConfig = {}

-- Configuration profiles
local configProfiles = {
	Production = {
		debug = { enableDebugMode = false, enableVerboseLogging = false },
		performance = { enablePerformanceMonitoring = true, enableAutomaticOptimization = true },
		effects = { effectQuality = "High" },
		ballistics = { ballisticsAccuracy = "High" },
	},

	Development = {
		debug = { enableDebugMode = true, enableVerboseLogging = true },
		performance = { enablePerformanceMonitoring = true, enablePerformanceAlerts = true },
		effects = { effectQuality = "Medium" },
		ballistics = { ballisticsAccuracy = "Medium" },
	},

	Performance = {
		effects = { effectQuality = "Low", maxActiveEffects = 50 },
		ballistics = { ballisticsAccuracy = "Low", trajectorySteps = 50 },
		performance = { enableAutomaticOptimization = true, targetFPS = 30 },
		audio = { enableReverb = false, enableEnvironmentalAudio = false },
	},

	HighEnd = {
		effects = { effectQuality = "Ultra", maxActiveEffects = 200 },
		ballistics = { ballisticsAccuracy = "Ultra", trajectorySteps = 200, enableSpinDrift = true },
		performance = { targetFPS = 120 },
		animations = { animationSmoothness = 0.05 },
	},

	Minimal = {
		weapon = { enableRecoil = false, enableSpread = false },
		animations = { enableSwayAnimation = false, enableRecoilAnimation = false },
		effects = { enableMuzzleFlash = false, enableShellEjection = false },
		ballistics = { enableAdvancedBallistics = false, enableEnvironmentalEffects = false },
		audio = { enableSpatialAudio = false, enableEnvironmentalAudio = false },
	},
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function ConfigurationSystem.initialize(): ()
	print("[ConfigurationSystem] Initializing configuration system...")

	-- Load default configuration
	activeConfig = ConfigurationSystem._deepCopy(defaultConfig)

	-- Apply any saved configuration
	ConfigurationSystem._loadSavedConfiguration()

	print("[ConfigurationSystem] Configuration system initialized")
end

-- ============================================================================
-- CONFIGURATION MANAGEMENT
-- ============================================================================

function ConfigurationSystem.setConfiguration(category: string, setting: string, value: any): ()
	if activeConfig[category] and activeConfig[category][setting] ~= nil then
		local oldValue = activeConfig[category][setting]
		activeConfig[category][setting] = value

		print(`[ConfigurationSystem] {category}.{setting}: {oldValue} → {value}`)

		-- Apply configuration change immediately
		ConfigurationSystem._applyConfigurationChange(category, setting, value)
	else
		warn(`[ConfigurationSystem] Invalid configuration: {category}.{setting}`)
	end
end

function ConfigurationSystem.getConfiguration(category: string?, setting: string?): any
	if category and setting then
		return activeConfig[category] and activeConfig[category][setting]
	elseif category then
		return activeConfig[category]
	else
		return activeConfig
	end
end

function ConfigurationSystem.setBulkConfiguration(config: { [string]: { [string]: any } }): ()
	for category, settings in pairs(config) do
		if activeConfig[category] then
			for setting, value in pairs(settings) do
				ConfigurationSystem.setConfiguration(category, setting, value)
			end
		end
	end
end

function ConfigurationSystem.resetToDefaults(): ()
	print("[ConfigurationSystem] Resetting to default configuration...")
	activeConfig = ConfigurationSystem._deepCopy(defaultConfig)
	ConfigurationSystem._applyFullConfiguration()
end

-- ============================================================================
-- CONFIGURATION PROFILES
-- ============================================================================

function ConfigurationSystem.loadProfile(profileName: string): ()
	local profile = configProfiles[profileName]
	if not profile then
		warn(`[ConfigurationSystem] Profile '{profileName}' not found`)
		return
	end

	print(`[ConfigurationSystem] Loading profile: {profileName}`)

	-- Reset to defaults first
	activeConfig = ConfigurationSystem._deepCopy(defaultConfig)

	-- Apply profile settings
	ConfigurationSystem.setBulkConfiguration(profile)

	print(`[ConfigurationSystem] Profile '{profileName}' loaded successfully`)
end

function ConfigurationSystem.createProfile(
	profileName: string,
	config: { [string]: { [string]: any } }
): ()
	configProfiles[profileName] = config
	print(`[ConfigurationSystem] Profile '{profileName}' created`)
end

function ConfigurationSystem.getAvailableProfiles(): { string }
	local profiles = {}
	for profileName, _ in pairs(configProfiles) do
		table.insert(profiles, profileName)
	end
	return profiles
end

-- ============================================================================
-- FEATURE TOGGLES
-- ============================================================================

function ConfigurationSystem.enableFeature(category: string, feature: string): ()
	ConfigurationSystem.setConfiguration(category, feature, true)
end

function ConfigurationSystem.disableFeature(category: string, feature: string): ()
	ConfigurationSystem.setConfiguration(category, feature, false)
end

function ConfigurationSystem.toggleFeature(category: string, feature: string): ()
	local currentValue = ConfigurationSystem.getConfiguration(category, feature)
	if type(currentValue) == "boolean" then
		ConfigurationSystem.setConfiguration(category, feature, not currentValue)
	end
end

function ConfigurationSystem.isFeatureEnabled(category: string, feature: string): boolean
	local value = ConfigurationSystem.getConfiguration(category, feature)
	return type(value) == "boolean" and value or false
end

-- ============================================================================
-- QUICK CONFIGURATION PRESETS
-- ============================================================================

function ConfigurationSystem.setPerformanceMode(): ()
	ConfigurationSystem.loadProfile("Performance")
end

function ConfigurationSystem.setHighQualityMode(): ()
	ConfigurationSystem.loadProfile("HighEnd")
end

function ConfigurationSystem.setDevelopmentMode(): ()
	ConfigurationSystem.loadProfile("Development")
end

function ConfigurationSystem.setProductionMode(): ()
	ConfigurationSystem.loadProfile("Production")
end

function ConfigurationSystem.setMinimalMode(): ()
	ConfigurationSystem.loadProfile("Minimal")
end

-- ============================================================================
-- CONFIGURATION APPLICATION
-- ============================================================================

function ConfigurationSystem._applyConfigurationChange(
	category: string,
	setting: string,
	value: any
): ()
	-- Apply configuration changes to relevant systems
	if category == "animations" then
		ConfigurationSystem._applyAnimationConfig(setting, value)
	elseif category == "effects" then
		ConfigurationSystem._applyEffectsConfig(setting, value)
	elseif category == "audio" then
		ConfigurationSystem._applyAudioConfig(setting, value)
	elseif category == "performance" then
		ConfigurationSystem._applyPerformanceConfig(setting, value)
	elseif category == "ballistics" then
		ConfigurationSystem._applyBallisticsConfig(setting, value)
	end
end

function ConfigurationSystem._applyAnimationConfig(setting: string, value: any): ()
	-- Apply to animation system if available
	local GSF = require(script.Parent.init)
	if GSF.AnimationSystem then
		if setting == "recoilIntensity" then
			GSF.AnimationSystem.setGlobalAnimationSettings({
				recoil = { verticalIntensity = value * 0.1, horizontalIntensity = value * 0.05 },
			})
		elseif setting == "swayIntensity" then
			GSF.AnimationSystem.setGlobalAnimationSettings({
				sway = { intensity = value * 0.02 },
			})
		end
	end
end

function ConfigurationSystem._applyEffectsConfig(setting: string, value: any): ()
	local GSF = require(script.Parent.init)
	if GSF.EffectsSystem then
		if setting == "effectQuality" then
			GSF.EffectsSystem.setQualityLevel(value)
		elseif setting == "maxActiveEffects" then
			GSF.EffectsSystem.setMaxActiveEffects(value)
		end
	end
end

function ConfigurationSystem._applyAudioConfig(setting: string, value: any): ()
	local GSF = require(script.Parent.init)
	if GSF.AudioSystem then
		if setting == "masterVolume" then
			GSF.AudioSystem.setGlobalVolume(value)
		elseif setting == "weaponVolume" then
			GSF.AudioSystem.setWeaponVolume(value)
		end
	end
end

function ConfigurationSystem._applyPerformanceConfig(setting: string, value: any): ()
	local GSF = require(script.Parent.init)
	if setting == "targetFPS" and GSF.PerformanceMonitor then
		GSF.PerformanceMonitor.setPerformanceTarget(value)
	elseif setting == "enableLODSystem" and GSF.LODSystem then
		GSF.LODSystem.enableAdaptiveLOD(value)
	end
end

function ConfigurationSystem._applyBallisticsConfig(setting: string, value: any): ()
	local GSF = require(script.Parent.init)
	if GSF.AdvancedBallistics then
		if setting == "ballisticsAccuracy" then
			GSF.AdvancedBallistics.setAccuracyLevel(value)
		end
	end
end

function ConfigurationSystem._applyFullConfiguration(): ()
	-- Apply all configuration settings
	for category, settings in pairs(activeConfig) do
		for setting, value in pairs(settings) do
			ConfigurationSystem._applyConfigurationChange(category, setting, value)
		end
	end
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function ConfigurationSystem._deepCopy(original: any): any
	local copy = {}
	for key, value in pairs(original) do
		if type(value) == "table" then
			copy[key] = ConfigurationSystem._deepCopy(value)
		else
			copy[key] = value
		end
	end
	return copy
end

function ConfigurationSystem._loadSavedConfiguration(): ()
	-- Load configuration from DataStore or other persistence
	-- This would be implemented based on your game's data storage system
end

function ConfigurationSystem.saveConfiguration(): ()
	-- Save current configuration
	-- This would be implemented based on your game's data storage system
	print("[ConfigurationSystem] Configuration saved")
end

-- ============================================================================
-- CONFIGURATION EXPORT/IMPORT
-- ============================================================================

function ConfigurationSystem.exportConfiguration(): string
	-- Export configuration as JSON string
	local HttpService = game:GetService("HttpService")
	return HttpService:JSONEncode(activeConfig)
end

function ConfigurationSystem.importConfiguration(configString: string): boolean
	local success, config = pcall(function()
		local HttpService = game:GetService("HttpService")
		return HttpService:JSONDecode(configString)
	end)

	if success and config then
		ConfigurationSystem.setBulkConfiguration(config)
		return true
	else
		warn("[ConfigurationSystem] Failed to import configuration")
		return false
	end
end

-- ============================================================================
-- CONFIGURATION VALIDATION
-- ============================================================================

function ConfigurationSystem.validateConfiguration(): { string }
	local issues = {}

	-- Validate configuration values
	for category, settings in pairs(activeConfig) do
		for setting, value in pairs(settings) do
			if defaultConfig[category] and defaultConfig[category][setting] ~= nil then
				local defaultType = type(defaultConfig[category][setting])
				local currentType = type(value)

				if defaultType ~= currentType then
					table.insert(issues, `{category}.{setting}: Expected {defaultType}, got {currentType}`)
				end
			end
		end
	end

	return issues
end

function ConfigurationSystem.printConfiguration(): ()
	print("\n🔧 === CURRENT GSF CONFIGURATION ===")

	for category, settings in pairs(activeConfig) do
		print(`\n[{category:upper()}]`)
		for setting, value in pairs(settings) do
			local valueStr = tostring(value)
			if type(value) == "boolean" then
				valueStr = value and "✅ ENABLED" or "❌ DISABLED"
			end
			print(`  {setting}: {valueStr}`)
		end
	end

	print("\n=== END CONFIGURATION ===\n")
end

return ConfigurationSystem
