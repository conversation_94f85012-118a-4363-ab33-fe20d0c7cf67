--!strict
--[[
	Gun System Framework - Performance Monitor

	This module provides comprehensive performance monitoring and optimization:
	- Real-time performance metrics tracking
	- Memory usage monitoring
	- Frame rate analysis
	- Component performance profiling
	- Automatic optimization suggestions
	- Performance alerts and warnings
]]

local RunService = game:GetService("RunService")
local Stats = game:GetService("Stats")

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- ============================================================================
-- PERFORMANCE MONITOR SYSTEM
-- ============================================================================

local PerformanceMonitor = {}
PerformanceMonitor.__index = PerformanceMonitor

-- Performance tracking data
local performanceData = {
  frameRate = {
    current = 0,
    average = 0,
    min = math.huge,
    max = 0,
    history = {},
    samples = 0,
  },

  memory = {
    current = 0,
    peak = 0,
    baseline = 0,
    history = {},
    gcCount = 0,
  },

  components = {
    activeCount = 0,
    totalCreated = 0,
    pooledCount = 0,
    attachmentOperations = 0,
    detachmentOperations = 0,
  },

  effects = {
    activeEffects = 0,
    totalEffectsCreated = 0,
    poolHitRate = 0,
    averageLifetime = 0,
  },

  audio = {
    activeSources = 0,
    totalSoundsPlayed = 0,
    poolHitRate = 0,
    averagePlaytime = 0,
  },

  projectiles = {
    activeProjectiles = 0,
    totalProjectilesFired = 0,
    averageLifetime = 0,
    collisionChecks = 0,
  },
}

-- Performance thresholds
local performanceThresholds = {
  frameRate = {
    excellent = 60,
    good = 45,
    acceptable = 30,
    poor = 20,
  },

  memory = {
    low = 50, -- MB
    medium = 100, -- MB
    high = 200, -- MB
    critical = 500, -- MB
  },

  components = {
    maxActive = 1000,
    maxPoolSize = 500,
  },

  effects = {
    maxActive = 100,
    maxPoolSize = 200,
  },

  audio = {
    maxActive = 50,
    maxPoolSize = 100,
  },
}

-- Monitoring state
local monitoringState = {
  isEnabled = false,
  updateInterval = 1.0, -- seconds
  lastUpdate = 0,
  alertsEnabled = true,
  profilingEnabled = false,
}

-- Performance alerts
local activeAlerts = {}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function PerformanceMonitor.initialize(): ()
  print("[PerformanceMonitor] Initializing performance monitoring system...")

  -- Set baseline memory usage
  performanceData.memory.baseline = collectgarbage("count")

  -- Start monitoring
  PerformanceMonitor.startMonitoring()

  print("[PerformanceMonitor] Performance monitoring system initialized")
end

function PerformanceMonitor.startMonitoring(): ()
  if monitoringState.isEnabled then
    return
  end

  monitoringState.isEnabled = true
  monitoringState.lastUpdate = tick()

  -- Start monitoring loop
  RunService.Heartbeat:Connect(PerformanceMonitor._updateMetrics)

  print("[PerformanceMonitor] Performance monitoring started")
end

function PerformanceMonitor.stopMonitoring(): ()
  monitoringState.isEnabled = false
  print("[PerformanceMonitor] Performance monitoring stopped")
end

-- ============================================================================
-- METRICS COLLECTION
-- ============================================================================

function PerformanceMonitor._updateMetrics(): ()
  if not monitoringState.isEnabled then
    return
  end

  local currentTime = tick()
  local deltaTime = currentTime - monitoringState.lastUpdate

  -- Update frame rate
  PerformanceMonitor._updateFrameRate(deltaTime)

  -- Update memory usage (less frequently)
  if deltaTime >= monitoringState.updateInterval then
    PerformanceMonitor._updateMemoryUsage()
    PerformanceMonitor._updateComponentMetrics()
    PerformanceMonitor._checkPerformanceAlerts()

    monitoringState.lastUpdate = currentTime
  end
end

function PerformanceMonitor._updateFrameRate(deltaTime: number): ()
  if deltaTime <= 0 then
    return
  end

  local currentFPS = 1 / deltaTime
  performanceData.frameRate.current = currentFPS

  -- Update statistics
  performanceData.frameRate.samples += 1
  performanceData.frameRate.min = math.min(performanceData.frameRate.min, currentFPS)
  performanceData.frameRate.max = math.max(performanceData.frameRate.max, currentFPS)

  -- Calculate rolling average
  local samples = performanceData.frameRate.samples
  local prevAverage = performanceData.frameRate.average
  performanceData.frameRate.average = (prevAverage * (samples - 1) + currentFPS) / samples

  -- Store in history (keep last 100 samples)
  table.insert(performanceData.frameRate.history, currentFPS)
  if #performanceData.frameRate.history > 100 then
    table.remove(performanceData.frameRate.history, 1)
  end
end

function PerformanceMonitor._updateMemoryUsage(): ()
  local currentMemory = collectgarbage("count") / 1024 -- Convert to MB
  performanceData.memory.current = currentMemory
  performanceData.memory.peak = math.max(performanceData.memory.peak, currentMemory)

  -- Store in history (keep last 60 samples = 1 minute at 1Hz)
  table.insert(performanceData.memory.history, currentMemory)
  if #performanceData.memory.history > 60 then
    table.remove(performanceData.memory.history, 1)
  end

  -- Check for garbage collection
  local gcCount = Stats.GCMemoryUsageKB
  if gcCount ~= performanceData.memory.gcCount then
    performanceData.memory.gcCount = gcCount
    print(
      string.format(
        "[PerformanceMonitor] Garbage collection occurred: %dMB",
        math.floor(currentMemory)
      )
    )
  end
end

function PerformanceMonitor._updateComponentMetrics(): ()
  -- These would be updated by the actual systems
  -- For now, we'll simulate some data

  -- Component metrics would be provided by ComponentRegistry
  -- Effect metrics would be provided by EffectsSystem
  -- Audio metrics would be provided by AudioSystem
  -- Projectile metrics would be provided by ProjectileSystem
end

-- ============================================================================
-- PERFORMANCE ANALYSIS
-- ============================================================================

function PerformanceMonitor.getPerformanceReport(): { [string]: any }
  return {
    timestamp = tick(),
    frameRate = {
      current = math.floor(performanceData.frameRate.current),
      average = math.floor(performanceData.frameRate.average),
      min = math.floor(performanceData.frameRate.min),
      max = math.floor(performanceData.frameRate.max),
      rating = PerformanceMonitor._getFrameRateRating(),
    },

    memory = {
      current = math.floor(performanceData.memory.current * 100) / 100,
      peak = math.floor(performanceData.memory.peak * 100) / 100,
      baseline = math.floor(performanceData.memory.baseline / 1024 * 100) / 100,
      rating = PerformanceMonitor._getMemoryRating(),
    },

    components = performanceData.components,
    effects = performanceData.effects,
    audio = performanceData.audio,
    projectiles = performanceData.projectiles,

    alerts = activeAlerts,
    recommendations = PerformanceMonitor._generateRecommendations(),
  }
end

function PerformanceMonitor._getFrameRateRating(): string
  local fps = performanceData.frameRate.average
  local thresholds = performanceThresholds.frameRate

  if fps >= thresholds.excellent then
    return "Excellent"
  elseif fps >= thresholds.good then
    return "Good"
  elseif fps >= thresholds.acceptable then
    return "Acceptable"
  elseif fps >= thresholds.poor then
    return "Poor"
  else
    return "Critical"
  end
end

function PerformanceMonitor._getMemoryRating(): string
  local memory = performanceData.memory.current
  local thresholds = performanceThresholds.memory

  if memory <= thresholds.low then
    return "Excellent"
  elseif memory <= thresholds.medium then
    return "Good"
  elseif memory <= thresholds.high then
    return "Acceptable"
  elseif memory <= thresholds.critical then
    return "Poor"
  else
    return "Critical"
  end
end

function PerformanceMonitor._generateRecommendations(): { string }
  local recommendations = {}

  -- Frame rate recommendations
  if performanceData.frameRate.average < performanceThresholds.frameRate.good then
    table.insert(recommendations, "Consider reducing visual effects quality")
    table.insert(recommendations, "Enable object pooling for better performance")
  end

  -- Memory recommendations
  if performanceData.memory.current > performanceThresholds.memory.medium then
    table.insert(recommendations, "Consider running garbage collection more frequently")
    table.insert(recommendations, "Check for memory leaks in component management")
  end

  -- Component recommendations
  if performanceData.components.activeCount > performanceThresholds.components.maxActive then
    table.insert(recommendations, "Too many active components - consider component pooling")
  end

  -- Effects recommendations
  if performanceData.effects.activeEffects > performanceThresholds.effects.maxActive then
    table.insert(recommendations, "Too many active effects - reduce effect lifetime or quality")
  end

  return recommendations
end

-- ============================================================================
-- PERFORMANCE ALERTS
-- ============================================================================

function PerformanceMonitor._checkPerformanceAlerts(): ()
  if not monitoringState.alertsEnabled then
    return
  end

  -- Clear old alerts
  activeAlerts = {}

  -- Frame rate alerts
  if performanceData.frameRate.average < performanceThresholds.frameRate.poor then
    table.insert(activeAlerts, {
      type = "FrameRate",
      severity = "High",
      message = string.format(
        "Low frame rate detected: %dfps",
        math.floor(performanceData.frameRate.average)
      ),
      timestamp = os.clock(),
    })
  end

  -- Memory alerts
  if performanceData.memory.current > performanceThresholds.memory.high then
    table.insert(activeAlerts, {
      type = "Memory",
      severity = "Medium",
      message = string.format(
        "High memory usage: %dMB",
        math.floor(performanceData.memory.current)
      ),
      timestamp = os.clock(),
    })
  end

  -- Component alerts
  if performanceData.components.activeCount > performanceThresholds.components.maxActive then
    table.insert(activeAlerts, {
      type = "Components",
      severity = "Medium",
      message = string.format(
        "Too many active components: %d",
        performanceData.components.activeCount
      ),
      timestamp = os.clock(),
    })
  end

  -- Print alerts
  for _, alert in ipairs(activeAlerts) do
    warn(string.format("[PerformanceMonitor] %s Alert: %s", alert.severity, alert.message))
  end
end

-- ============================================================================
-- OPTIMIZATION TOOLS
-- ============================================================================

function PerformanceMonitor.forceGarbageCollection(): number
  local beforeMemory = collectgarbage("count")
  local _ = collectgarbage("collect")
  local afterMemory = collectgarbage("count")

  local freed = beforeMemory - afterMemory
  print(string.format("[PerformanceMonitor] Garbage collection freed %dKB", math.floor(freed)))

  return freed
end

function PerformanceMonitor.optimizeMemoryUsage(): ()
  print("[PerformanceMonitor] Running memory optimization...")

  -- Force garbage collection
  local freed = PerformanceMonitor.forceGarbageCollection()

  -- Additional optimization steps would go here
  -- - Clear unused object pools
  -- - Cleanup expired effects
  -- - Remove inactive audio sources

  print(
    string.format(
      "[PerformanceMonitor] Memory optimization complete. Freed %dKB",
      math.floor(freed)
    )
  )
end

function PerformanceMonitor.getOptimizationSuggestions(): { string }
  local suggestions = {}
  local report = PerformanceMonitor.getPerformanceReport()

  -- Frame rate optimizations
  if report.frameRate.rating == "Poor" or report.frameRate.rating == "Critical" then
    table.insert(suggestions, "Reduce visual effects quality")
    table.insert(suggestions, "Implement Level of Detail (LOD) system")
    table.insert(suggestions, "Optimize projectile physics calculations")
  end

  -- Memory optimizations
  if report.memory.rating == "Poor" or report.memory.rating == "Critical" then
    table.insert(suggestions, "Increase object pool sizes")
    table.insert(suggestions, "Implement automatic garbage collection")
    table.insert(suggestions, "Reduce texture quality")
  end

  -- General optimizations
  table.insert(suggestions, "Enable performance monitoring alerts")
  table.insert(suggestions, "Use object pooling for frequently created objects")
  table.insert(suggestions, "Implement distance-based LOD for effects")

  return suggestions
end

-- ============================================================================
-- PROFILING TOOLS
-- ============================================================================

function PerformanceMonitor.startProfiling(): ()
  monitoringState.profilingEnabled = true
  print("[PerformanceMonitor] Performance profiling started")
end

function PerformanceMonitor.stopProfiling(): ()
  monitoringState.profilingEnabled = false
  print("[PerformanceMonitor] Performance profiling stopped")
end

function PerformanceMonitor.profileFunction(functionName: string, func: () -> any): any
  if not monitoringState.profilingEnabled then
    return func()
  end

  local startTime = tick()
  local startMemory = collectgarbage("count")

  local result = func()

  local endTime = tick()
  local endMemory = collectgarbage("count")

  local executionTime = (endTime - startTime) * 1000 -- Convert to milliseconds
  local memoryDelta = endMemory - startMemory

  print(
    string.format(
      "[PerformanceMonitor] Profile %s: %.2fms, %dKB",
      functionName,
      math.floor(executionTime * 100) / 100,
      math.floor(memoryDelta)
    )
  )

  return result
end

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

function PerformanceMonitor.setUpdateInterval(interval: number): ()
  monitoringState.updateInterval = math.max(0.1, interval)
  print(
    string.format("[PerformanceMonitor] Update interval set to %fs", monitoringState.updateInterval)
  )
end

function PerformanceMonitor.enableAlerts(enabled: boolean): ()
  monitoringState.alertsEnabled = enabled
  print(`[PerformanceMonitor] Alerts {enabled and "enabled" or "disabled"}`)
end

function PerformanceMonitor.setThreshold(category: string, threshold: string, value: number): ()
  if performanceThresholds[category] and performanceThresholds[category][threshold] then
    performanceThresholds[category][threshold] = value
    print(`[PerformanceMonitor] Threshold {category}.{threshold} set to {value}`)
  else
    warn(`[PerformanceMonitor] Invalid threshold: {category}.{threshold}`)
  end
end

-- ============================================================================
-- METRICS REPORTING
-- ============================================================================

function PerformanceMonitor.printPerformanceReport(): ()
  local report = PerformanceMonitor.getPerformanceReport()

  print("\n📊 === PERFORMANCE REPORT ===")
  print(`Timestamp: {os.date("%H:%M:%S", report.timestamp)}`)

  print("\n🖼️ Frame Rate:")
  print(`  Current: {report.frameRate.current} fps`)
  print(`  Average: {report.frameRate.average} fps`)
  print(`  Range: {report.frameRate.min} - {report.frameRate.max} fps`)
  print(`  Rating: {report.frameRate.rating}`)

  print("\n💾 Memory Usage:")
  print(`  Current: {report.memory.current} MB`)
  print(`  Peak: {report.memory.peak} MB`)
  print(`  Baseline: {report.memory.baseline} MB`)
  print(`  Rating: {report.memory.rating}`)

  print("\n🔧 Components:")
  print(`  Active: {report.components.activeCount}`)
  print(`  Total Created: {report.components.totalCreated}`)
  print(`  Pooled: {report.components.pooledCount}`)

  print("\n✨ Effects:")
  print(`  Active: {report.effects.activeEffects}`)
  print(`  Total Created: {report.effects.totalEffectsCreated}`)
  print(`  Pool Hit Rate: {math.floor(report.effects.poolHitRate * 100)}%`)

  print("\n🔊 Audio:")
  print(`  Active Sources: {report.audio.activeSources}`)
  print(`  Total Played: {report.audio.totalSoundsPlayed}`)
  print(`  Pool Hit Rate: {math.floor(report.audio.poolHitRate * 100)}%`)

  if #report.alerts > 0 then
    print("\n⚠️ Active Alerts:")
    for _, alert in ipairs(report.alerts) do
      print(`  {alert.severity}: {alert.message}`)
    end
  end

  if #report.recommendations > 0 then
    print("\n💡 Recommendations:")
    for _, recommendation in ipairs(report.recommendations) do
      print(`  • {recommendation}`)
    end
  end

  print("=== END REPORT ===\n")
end

-- ============================================================================
-- EXTERNAL METRICS INTERFACE
-- ============================================================================

function PerformanceMonitor.recordComponentMetric(metric: string, value: number): ()
  if performanceData.components[metric] then
    performanceData.components[metric] = value
  end
end

function PerformanceMonitor.recordEffectMetric(metric: string, value: number): ()
  if performanceData.effects[metric] then
    performanceData.effects[metric] = value
  end
end

function PerformanceMonitor.recordAudioMetric(metric: string, value: number): ()
  if performanceData.audio[metric] then
    performanceData.audio[metric] = value
  end
end

function PerformanceMonitor.recordProjectileMetric(metric: string, value: number): ()
  if performanceData.projectiles[metric] then
    performanceData.projectiles[metric] = value
  end
end

return PerformanceMonitor
