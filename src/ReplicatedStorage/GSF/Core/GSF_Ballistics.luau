--!strict
--[[
	Gun System Framework - Ballistics System

	This module provides realistic ballistics calculations including:
	- Advanced bullet drop compensation
	- Wind effects and environmental factors
	- Penetration mechanics for different materials
	- Ballistic coefficient calculations
	- Terminal ballistics and energy transfer
]]

local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type Vector3 = Types.Vector3
type Projectile = Types.Projectile
type BallisticData = Types.BallisticData
type EnvironmentalConditions = Types.EnvironmentalConditions
type PenetrationResult = Types.PenetrationResult
type TerminalBallistics = Types.TerminalBallistics

-- ============================================================================
-- ADVANCED BALLISTICS SYSTEM
-- ============================================================================

local AdvancedBallistics = {}
AdvancedBallistics.__index = AdvancedBallistics

-- Physical constants
local GRAVITY = 9.81 -- m/s²
local SPEED_OF_SOUND = 343 -- m/s at 20°C

-- Ballistic coefficients for common projectiles
local BALLISTIC_COEFFICIENTS = {
	["556x45"] = 0.151, -- 5.56x45mm NATO
	["762x51"] = 0.200, -- 7.62x51mm NATO
	["308win"] = 0.200, -- .308 Winchester
	["9x19"] = 0.125, -- 9mm Parabellum
	["45acp"] = 0.095, -- .45 ACP
	["50bmg"] = 0.295, -- .50 BMG
}

-- Material penetration properties
local MATERIAL_PROPERTIES = {
	Wood = { density = 600, hardness = 2, thickness_modifier = 1.0 },
	Concrete = { density = 2400, hardness = 8, thickness_modifier = 1.5 },
	Metal = { density = 7800, hardness = 9, thickness_modifier = 2.0 },
	Glass = { density = 2500, hardness = 6, thickness_modifier = 0.8 },
	Plastic = { density = 950, hardness = 3, thickness_modifier = 0.9 },
	Fabric = { density = 200, hardness = 1, thickness_modifier = 0.5 },
	Water = { density = 1000, hardness = 1, thickness_modifier = 3.0 },
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function AdvancedBallistics.initialize(): ()
	print("[AdvancedBallistics] Initializing advanced ballistics system...")
	print("[AdvancedBallistics] Advanced ballistics system initialized")
end

-- ============================================================================
-- BALLISTIC CALCULATIONS
-- ============================================================================

function AdvancedBallistics.calculateTrajectory(
	projectile: Projectile,
	environmental: EnvironmentalConditions,
	timeStep: number,
	maxTime: number
): { Vector3 }
	local trajectory = {}
	local position = projectile.position
	local velocity = projectile.velocity
	local mass = projectile.mass
	local diameter = projectile.diameter
	local bc = BALLISTIC_COEFFICIENTS[projectile.ammunitionType] or 0.15

	local time = 0
	while time < maxTime do
		-- Calculate air density at current altitude
		local airDensity = AdvancedBallistics._calculateAirDensity(position.Y, environmental)

		-- Calculate drag force
		local dragForce = AdvancedBallistics._calculateDragForce(velocity, airDensity, diameter, bc)

		-- Calculate wind effect
		local windEffect =
			AdvancedBallistics._calculateWindEffect(velocity, environmental.wind, airDensity)

		-- Calculate gravity effect
		local gravityEffect = { X = 0, Y = -GRAVITY * timeStep, Z = 0 }

		-- Apply forces to velocity
		local accelerationDrag = {
			X = -dragForce.X / mass,
			Y = -dragForce.Y / mass,
			Z = -dragForce.Z / mass,
		}

		velocity = {
			X = velocity.X + (accelerationDrag.X + windEffect.X + gravityEffect.X) * timeStep,
			Y = velocity.Y + (accelerationDrag.Y + windEffect.Y + gravityEffect.Y) * timeStep,
			Z = velocity.Z + (accelerationDrag.Z + windEffect.Z + gravityEffect.Z) * timeStep,
		}

		-- Update position
		position = {
			X = position.X + velocity.X * timeStep,
			Y = position.Y + velocity.Y * timeStep,
			Z = position.Z + velocity.Z * timeStep,
		}

		-- Store trajectory point
		table.insert(trajectory, {
			X = position.X,
			Y = position.Y,
			Z = position.Z,
			time = time,
			velocity = math.sqrt(velocity.X ^ 2 + velocity.Y ^ 2 + velocity.Z ^ 2),
		})

		time += timeStep

		-- Stop if projectile hits ground or loses too much velocity
		if position.Y < 0 or math.sqrt(velocity.X ^ 2 + velocity.Y ^ 2 + velocity.Z ^ 2) < 50 then
			break
		end
	end

	return trajectory
end

function AdvancedBallistics.calculateBulletDrop(
	muzzleVelocity: number,
	range: number,
	ammunitionType: string,
	environmental: EnvironmentalConditions
): number
	local bc = BALLISTIC_COEFFICIENTS[ammunitionType] or 0.15
	local timeOfFlight =
		AdvancedBallistics._calculateTimeOfFlight(muzzleVelocity, range, bc, environmental)

	-- Calculate drop using simplified ballistic equation
	local drop = 0.5 * GRAVITY * timeOfFlight ^ 2

	-- Apply environmental corrections
	local altitudeCorrection = (environmental.altitude or 0) / 1000 * 0.02 -- 2% per 1000m
	local temperatureCorrection = (environmental.temperature or 20 - 15) / 10 * 0.01 -- 1% per 10°C

	drop = drop * (1 + altitudeCorrection + temperatureCorrection)

	return drop
end

function AdvancedBallistics.calculateWindDrift(
	muzzleVelocity: number,
	range: number,
	windSpeed: Vector3,
	ammunitionType: string
): Vector3
	local bc = BALLISTIC_COEFFICIENTS[ammunitionType] or 0.15
	local timeOfFlight = range / muzzleVelocity -- Simplified

	-- Calculate wind drift components
	local crossWind = math.sqrt(windSpeed.X ^ 2 + windSpeed.Z ^ 2)
	local windAngle = math.atan2(windSpeed.Z, windSpeed.X)

	-- Wind drift formula (simplified)
	local driftMagnitude = (crossWind * timeOfFlight ^ 2) / (2 * bc)

	return {
		X = driftMagnitude * math.cos(windAngle),
		Y = 0,
		Z = driftMagnitude * math.sin(windAngle),
	}
end

-- ============================================================================
-- PENETRATION MECHANICS
-- ============================================================================

function AdvancedBallistics.calculatePenetration(
	projectile: Projectile,
	material: string,
	thickness: number,
	angle: number
): PenetrationResult
	local materialProps = MATERIAL_PROPERTIES[material]
	if not materialProps then
		return {
			penetrated = false,
			energyLoss = 1.0,
			deflection = { X = 0, Y = 0, Z = 0 },
			fragments = {},
		}
	end

	-- Calculate projectile energy
	local velocity =
		math.sqrt(projectile.velocity.X ^ 2 + projectile.velocity.Y ^ 2 + projectile.velocity.Z ^ 2)
	local kineticEnergy = 0.5 * projectile.mass * velocity ^ 2

	-- Calculate effective thickness based on angle
	local effectiveThickness = thickness / math.cos(math.rad(angle))
	effectiveThickness *= materialProps.thickness_modifier

	-- Calculate penetration energy requirement
	local penetrationEnergy = AdvancedBallistics._calculatePenetrationEnergy(
		materialProps,
		effectiveThickness,
		projectile.diameter
	)

	-- Determine if penetration occurs
	local penetrated = kineticEnergy > penetrationEnergy
	local energyLoss = math.min(1.0, penetrationEnergy / kineticEnergy)

	-- Calculate deflection for non-perpendicular impacts
	local deflection = { X = 0, Y = 0, Z = 0 }
	if angle > 15 and not penetrated then
		local deflectionAngle = math.rad(angle * 0.5) -- Simplified deflection
		deflection = {
			X = math.sin(deflectionAngle) * velocity * 0.1,
			Y = 0,
			Z = 0,
		}
	end

	-- Generate fragments for certain materials
	local fragments = {}
	if penetrated and (material == "Glass" or material == "Concrete") then
		fragments = AdvancedBallistics._generateFragments(material, thickness, kineticEnergy)
	end

	return {
		penetrated = penetrated,
		energyLoss = energyLoss,
		deflection = deflection,
		fragments = fragments,
		exitVelocity = penetrated and velocity * math.sqrt(1 - energyLoss) or 0,
	}
end

function AdvancedBallistics.calculateRicochet(
	projectile: Projectile,
	surfaceMaterial: string,
	impactAngle: number
): {
	ricochet: boolean,
	newDirection: Vector3,
	energyRetained: number,
	}
	local materialProps = MATERIAL_PROPERTIES[surfaceMaterial]
	if not materialProps then
		return { ricochet = false, newDirection = { X = 0, Y = 0, Z = 0 }, energyRetained = 0 }
	end

	-- Calculate critical ricochet angle based on material hardness
	local criticalAngle = 15 + (materialProps.hardness * 5) -- Harder materials = higher critical angle

	-- Determine if ricochet occurs
	local ricochet = impactAngle < criticalAngle

	if not ricochet then
		return { ricochet = false, newDirection = { X = 0, Y = 0, Z = 0 }, energyRetained = 0 }
	end

	-- Calculate ricochet direction (simplified)
	local reflectionAngle = impactAngle * 0.8 -- Some energy loss
	local energyRetained = 0.3 + (criticalAngle - impactAngle) / criticalAngle * 0.4

	-- Calculate new direction vector
	local newDirection = {
		X = projectile.velocity.X * 0.9,
		Y = math.abs(projectile.velocity.Y) * math.sin(math.rad(reflectionAngle)),
		Z = projectile.velocity.Z * 0.9,
	}

	return {
		ricochet = true,
		newDirection = newDirection,
		energyRetained = energyRetained,
	}
end

-- ============================================================================
-- TERMINAL BALLISTICS
-- ============================================================================

function AdvancedBallistics.calculateTerminalEffects(
	projectile: Projectile,
	target: any
): TerminalBallistics
	local velocity =
		math.sqrt(projectile.velocity.X ^ 2 + projectile.velocity.Y ^ 2 + projectile.velocity.Z ^ 2)
	local kineticEnergy = 0.5 * projectile.mass * velocity ^ 2

	-- Calculate damage based on energy transfer
	local energyTransfer = AdvancedBallistics._calculateEnergyTransfer(projectile, target)
	local damage = energyTransfer * 0.001 -- Convert to game damage units

	-- Calculate wound channel characteristics
	local temporaryCavity =
		AdvancedBallistics._calculateTemporaryCavity(velocity, projectile.diameter)
	local permanentCavity = temporaryCavity * 0.3 -- Permanent cavity is smaller

	-- Calculate stopping power
	local stoppingPower =
		AdvancedBallistics._calculateStoppingPower(kineticEnergy, projectile.diameter)

	return {
		damage = damage,
		energyTransfer = energyTransfer,
		temporaryCavity = temporaryCavity,
		permanentCavity = permanentCavity,
		stoppingPower = stoppingPower,
		penetrationDepth = AdvancedBallistics._calculatePenetrationDepth(velocity, projectile.mass),
	}
end

-- ============================================================================
-- PRIVATE HELPER FUNCTIONS
-- ============================================================================

function AdvancedBallistics._calculateAirDensity(
	altitude: number,
	environmental: EnvironmentalConditions
): number
	local temperature = environmental.temperature or 20 -- °C
	local pressure = environmental.pressure or 101325 -- Pa
	local humidity = environmental.humidity or 0.5

	-- Simplified air density calculation
	local temperatureK = temperature + 273.15
	local densityDry = pressure / (287.05 * temperatureK)

	-- Humidity correction (simplified)
	local densityHumid = densityDry * (1 - 0.378 * humidity * 0.01)

	-- Altitude correction
	local altitudeFactor = math.exp(-altitude / 8400) -- Scale height ~8.4km

	return densityHumid * altitudeFactor
end

function AdvancedBallistics._calculateDragForce(
	velocity: Vector3,
	airDensity: number,
	diameter: number,
	bc: number
): Vector3
	local speed = math.sqrt(velocity.X ^ 2 + velocity.Y ^ 2 + velocity.Z ^ 2)
	local area = math.pi * (diameter / 2) ^ 2

	-- Drag coefficient varies with Mach number
	local machNumber = speed / SPEED_OF_SOUND
	local dragCoefficient = AdvancedBallistics._getDragCoefficient(machNumber)

	-- Drag force magnitude
	local dragMagnitude = 0.5 * airDensity * speed ^ 2 * area * dragCoefficient / bc

	-- Apply drag in opposite direction of velocity
	local unitVelocity = {
		X = velocity.X / speed,
		Y = velocity.Y / speed,
		Z = velocity.Z / speed,
	}

	return {
		X = dragMagnitude * unitVelocity.X,
		Y = dragMagnitude * unitVelocity.Y,
		Z = dragMagnitude * unitVelocity.Z,
	}
end

function AdvancedBallistics._calculateWindEffect(
	velocity: Vector3,
	wind: Vector3,
	airDensity: number
): Vector3
	-- Simplified wind effect calculation
	local relativeWind = {
		X = wind.X - velocity.X,
		Y = wind.Y - velocity.Y,
		Z = wind.Z - velocity.Z,
	}

	local windSpeed = math.sqrt(relativeWind.X ^ 2 + relativeWind.Y ^ 2 + relativeWind.Z ^ 2)
	local windEffect = windSpeed * airDensity * 0.0001 -- Simplified coefficient

	return {
		X = relativeWind.X * windEffect,
		Y = relativeWind.Y * windEffect,
		Z = relativeWind.Z * windEffect,
	}
end

function AdvancedBallistics._getDragCoefficient(machNumber: number): number
	-- Simplified drag coefficient curve
	if machNumber < 0.8 then
		return 0.15 + machNumber * 0.1
	elseif machNumber < 1.2 then
		return 0.25 + (machNumber - 0.8) * 0.5 -- Transonic spike
	else
		return 0.45 - (machNumber - 1.2) * 0.05 -- Supersonic decrease
	end
end

function AdvancedBallistics._calculateTimeOfFlight(
	muzzleVelocity: number,
	range: number,
	bc: number,
	environmental: EnvironmentalConditions
): number
	-- Simplified time of flight calculation
	local averageVelocity = muzzleVelocity * 0.85 -- Account for drag
	return range / averageVelocity
end

function AdvancedBallistics._calculatePenetrationEnergy(
	materialProps: any,
	thickness: number,
	diameter: number
): number
	-- Simplified penetration energy calculation
	local volume = math.pi * (diameter / 2) ^ 2 * thickness
	return materialProps.density * materialProps.hardness * volume * 1000
end

function AdvancedBallistics._generateFragments(
	material: string,
	thickness: number,
	energy: number
): { any }
	local fragments = {}
	local fragmentCount = math.min(10, math.floor(energy / 1000))

	for i = 1, fragmentCount do
		table.insert(fragments, {
			mass = thickness * 0.001 * math.random(),
			velocity = math.random() * 100,
			direction = {
				X = (math.random() - 0.5) * 2,
				Y = (math.random() - 0.5) * 2,
				Z = (math.random() - 0.5) * 2,
			},
		})
	end

	return fragments
end

function AdvancedBallistics._calculateEnergyTransfer(projectile: Projectile, target: any): number
	local velocity =
		math.sqrt(projectile.velocity.X ^ 2 + projectile.velocity.Y ^ 2 + projectile.velocity.Z ^ 2)
	local kineticEnergy = 0.5 * projectile.mass * velocity ^ 2

	-- Energy transfer depends on projectile design and target
	local transferEfficiency = 0.7 -- 70% energy transfer (typical for FMJ)

	return kineticEnergy * transferEfficiency
end

function AdvancedBallistics._calculateTemporaryCavity(velocity: number, diameter: number): number
	-- Temporary cavity size based on velocity and diameter
	return diameter * (velocity / 300) ^ 1.5 * 0.001 -- Convert to meters
end

function AdvancedBallistics._calculateStoppingPower(energy: number, diameter: number): number
	-- Simplified stopping power calculation
	return energy * diameter * 0.0001
end

function AdvancedBallistics._calculatePenetrationDepth(velocity: number, mass: number): number
	-- Simplified penetration depth in soft tissue
	return (velocity * mass) / 10000 -- Simplified formula
end

return AdvancedBallistics
