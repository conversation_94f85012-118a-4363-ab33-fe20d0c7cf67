--!strict
--[[
	Gun System Framework - Weapon Entity System

	This module implements the core weapon entity that manages:
	- Weapon state and statistics
	- Component attachment and detachment
	- Weapon operations (fire, reload, inspect)
	- State validation and updates
]]

local CoreTypes = require(script.Parent.Parent.Types:WaitForChild("GSF_Core"))
local Types = require(script.Parent.Parent.Types:WaitForChild("GSF_Types"))

-- Type imports
type Weapon = Types.Weapon
type WeaponState = Types.WeaponState
type WeaponStatistics = Types.WeaponStatistics
type WeaponComponent = Types.WeaponComponent
type WeaponCategory = Types.WeaponCategory
type ComponentType = Types.ComponentType
type AttachmentPoint = Types.AttachmentPoint
type FireResult = Types.FireResult
type ReloadResult = Types.ReloadResult
type InspectionResult = Types.InspectionResult
type AttachResult = Types.AttachResult
type DetachResult = Types.DetachResult
type ValidationResult = Types.ValidationResult
type Vector3 = Types.Vector3
type FireMode = Types.FireMode
type AmmunitionType = Types.AmmunitionType

-- ============================================================================
-- WEAPON ENTITY IMPLEMENTATION
-- ============================================================================

local WeaponEntity = {}
WeaponEntity.__index = WeaponEntity

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function WeaponEntity.new(config: { [string]: any }): Weapon
  assert(config.id, "Weapon ID is required")
  assert(config.name, "Weapon name is required")
  assert(config.category, "Weapon category is required")
  assert(config.baseStatistics, "Base weapon statistics are required")

  -- Validate weapon category
  if not CoreTypes.TypeGuards.isValidWeaponCategory(config.category) then
    error(string.format("Invalid weapon category: %s", config.category))
  end

  local weapon: Weapon = setmetatable({
    -- Identity
    id = config.id,
    name = config.name,
    category = config.category,
    version = config.version or "1.0.0",

    -- Component system
    components = {},
    attachmentPoints = config.attachmentPoints or {},

    -- State management
    state = WeaponEntity._createDefaultState(config),
    baseStatistics = config.baseStatistics,
    currentStatistics = table.clone(config.baseStatistics),

    -- Runtime properties
    owner = nil,
    instance = nil,
    lastUpdateTime = os.clock(),
  }, WeaponEntity)

  -- Initialize attachment points
  WeaponEntity._initializeAttachmentPoints(weapon)

  -- Initialize animation system
  weapon.animationSettings = config.animationSettings or {}
  weapon.animator = nil -- Will be created when weapon model is available

  return weapon
end

-- ============================================================================
-- PRIVATE HELPER FUNCTIONS
-- ============================================================================

function WeaponEntity._createDefaultState(config: { [string]: any }): WeaponState
  local baseStats = config.baseStatistics

  return {
    ammunition = {
      chambered = 0,
      magazine = 0,
      reserve = config.initialReserve or 120,
      maxMagazine = baseStats.compatibleAmmo and 30 or 30, -- Default to 30
      maxReserve = config.maxReserve or 300,
    },

    condition = {
      durability = 1.0,
      cleanliness = 1.0,
    },

    timing = {
      lastFired = 0,
      reloadStarted = nil,
      cycleTime = 60 / (baseStats.fireRate or 600), -- Convert RPM to seconds
      cooldownEnd = nil,
    },

    modes = {
      fireMode = "Semi",
      safety = true,
      aimingDownSights = false,
      bipodDeployed = false,
      laserActive = false,
      flashlightActive = false,
    },

    flags = {
      isReloading = false,
      isFiring = false,
      isInspecting = false,
    },
  }
end

function WeaponEntity._initializeAttachmentPoints(weapon: Weapon): ()
  -- Create default attachment points if none provided
  if not weapon.attachmentPoints or next(weapon.attachmentPoints) == nil then
    weapon.attachmentPoints = {
      barrel_mount = {
        name = "barrel_mount",
        position = { X = 0, Y = 0, Z = 0 },
        orientation = { X = 0, Y = 0, Z = 0 },
        compatibleTypes = { "Barrel" },
        occupied = false,
        component = nil,
      },
      magazine_well = {
        name = "magazine_well",
        position = { X = 0, Y = -2, Z = 0 },
        orientation = { X = 0, Y = 0, Z = 0 },
        compatibleTypes = { "Magazine" },
        occupied = false,
        component = nil,
      },
      optic_rail = {
        name = "optic_rail",
        position = { X = 0, Y = 1, Z = 0 },
        orientation = { X = 0, Y = 0, Z = 0 },
        compatibleTypes = { "Sight", "Scope" },
        occupied = false,
        component = nil,
      },
      stock_mount = {
        name = "stock_mount",
        position = { X = 0, Y = 0, Z = -8 },
        orientation = { X = 0, Y = 0, Z = 0 },
        compatibleTypes = { "Stock" },
        occupied = false,
        component = nil,
      },
    }
  end
end

-- ============================================================================
-- CORE WEAPON METHODS
-- ============================================================================

function WeaponEntity:fire(origin: Vector3, direction: Vector3): FireResult
  local currentTime = os.clock()

  -- Check if weapon can fire
  if not self:canFire() then
    return {
      success = false,
      errorReason = "Cannot fire weapon in current state",
      projectileId = nil,
      muzzleFlash = false,
      shellEjected = false,
      origin = origin,
      direction = direction,
      velocity = 0,
      ammunitionConsumed = 0,
      durabilityLoss = 0,
      heatGenerated = 0,
      recoilVector = { X = 0, Y = 0, Z = 0 },
    }
  end

  -- Check ammunition
  if self.state.ammunition.chambered == 0 and self.state.ammunition.magazine == 0 then
    return {
      success = false,
      errorReason = "No ammunition available",
      projectileId = nil,
      muzzleFlash = false,
      shellEjected = false,
      origin = origin,
      direction = direction,
      velocity = 0,
      ammunitionConsumed = 0,
      durabilityLoss = 0,
      heatGenerated = 0,
      recoilVector = { X = 0, Y = 0, Z = 0 },
    }
  end

  -- Consume ammunition
  local ammunitionConsumed = 0
  if self.state.ammunition.chambered > 0 then
    self.state.ammunition.chambered -= 1
    ammunitionConsumed = 1
  elseif self.state.ammunition.magazine > 0 then
    self.state.ammunition.magazine -= 1
    ammunitionConsumed = 1
  end

  -- Calculate weapon effects
  local durabilityLoss = self.currentStatistics.durabilityLoss or 0.0001
  local heatGenerated = self.currentStatistics.heatGeneration or 0.1

  -- Apply weapon wear
  self.state.condition.durability = math.max(0, self.state.condition.durability - durabilityLoss)
  -- self.state.condition.temperature += heatGenerated -- Removed for simplified system
  self.state.condition.fouling += 0.001

  -- Calculate recoil
  local recoilVector = self:_calculateRecoil()

  -- Update timing
  self.state.timing.lastFired = currentTime
  self.state.flags.isFiring = true

  -- Notify components
  local fireData = {
    weapon = self,
    origin = origin,
    direction = direction,
    timestamp = currentTime,
    ammunition = self.currentStatistics.defaultAmmo,
    fireMode = self.state.modes.fireMode,
  }

  for _, component in pairs(self.components) do
    component:onFire(self, fireData)
  end

  -- Generate projectile ID
  local projectileId = string.format("projectile_%s_%f", self.id, currentTime)

  -- Create visual and audio effects
  local GSF = require(script.Parent)
  if GSF.EffectsSystem then
    GSF.EffectsSystem.createMuzzleFlash(self, fireData)
    GSF.EffectsSystem.createShellEjection(self, fireData)
  end
  if GSF.AudioSystem then
    GSF.AudioSystem.playWeaponFire(self, fireData)
  end

  -- Play recoil animation
  if self.animator then
    self.animator:playRecoilAnimation(fireData)
  end

  return {
    success = true,
    projectileId = projectileId,
    muzzleFlash = true,
    shellEjected = true,
    origin = origin,
    direction = direction,
    velocity = self.currentStatistics.muzzleVelocity,
    ammunitionConsumed = ammunitionConsumed,
    durabilityLoss = durabilityLoss,
    heatGenerated = heatGenerated,
    recoilVector = recoilVector,
    soundId = string.format("fire_sound_%s", self.category),
    effectIds = {
      string.format("muzzle_flash_%s", self.id),
      string.format("shell_eject_%s", self.id),
    },
  }
end

function WeaponEntity:reload(forceEmpty: boolean?): ReloadResult
  local currentTime = os.clock()

  -- Check if weapon can reload
  if not self:canReload() then
    return {
      success = false,
      reloadType = "Tactical",
      duration = 0,
      ammunitionAdded = 0,
      reserveConsumed = 0,
      chamberedRound = false,
      errorReason = "Cannot reload weapon in current state",
    }
  end

  -- Determine reload type
  local reloadType: "Tactical" | "Empty" | "Individual"
  local hasChamberedRound = self.state.ammunition.chambered > 0

  if
    forceEmpty or (self.state.ammunition.magazine == 0 and self.state.ammunition.chambered == 0)
  then
    reloadType = "Empty"
  else
    reloadType = "Tactical"
  end

  -- Calculate ammunition to add
  local maxCapacity = self.state.ammunition.maxMagazine
  local currentTotal = self.state.ammunition.magazine + self.state.ammunition.chambered
  local spaceAvailable = maxCapacity - currentTotal
  local ammunitionToAdd = math.min(spaceAvailable, self.state.ammunition.reserve)

  -- Perform reload
  self.state.ammunition.magazine += ammunitionToAdd
  self.state.ammunition.reserve -= ammunitionToAdd

  -- Chamber a round if magazine was empty
  if not hasChamberedRound and self.state.ammunition.magazine > 0 then
    self.state.ammunition.chambered = 1
    self.state.ammunition.magazine -= 1
  end

  -- Calculate reload duration
  local reloadTime = self.currentStatistics.reloadTime
  local duration = reloadType == "Empty" and reloadTime.empty or reloadTime.tactical

  -- Set reload state
  self.state.flags.isReloading = true
  self.state.timing.reloadStarted = currentTime

  -- Notify components
  local reloadData = {
    weapon = self,
    reloadType = reloadType,
    timestamp = currentTime,
    ammunitionType = self.currentStatistics.defaultAmmo,
    roundsToLoad = ammunitionToAdd,
  }

  for _, component in pairs(self.components) do
    component:onReload(self, reloadData)
  end

  -- Play reload audio effects
  local GSF = require(script.Parent)
  if GSF.AudioSystem then
    GSF.AudioSystem.playWeaponReload(self, reloadData)
  end

  -- Play reload animation
  if self.animator then
    self.animator:playReloadAnimation(reloadType:lower())
  end

  -- Schedule reload completion
  task.delay(duration, function()
    self.state.flags.isReloading = false
    self.state.timing.reloadStarted = nil
  end)

  return {
    success = true,
    reloadType = reloadType,
    duration = duration,
    ammunitionAdded = ammunitionToAdd,
    reserveConsumed = ammunitionToAdd,
    chamberedRound = self.state.ammunition.chambered > 0,
    animationId = string.format("reload_%s_%s", reloadType, self.category),
    soundIds = {
      string.format("reload_start_%s", self.category),
      string.format("reload_end_%s", self.category),
    },
  }
end

function WeaponEntity:inspect(): InspectionResult
  -- Calculate condition levels
  local durabilityPercent = math.floor(self.state.condition.durability * 100)

  local cleanlinessLevel: "Pristine" | "Clean" | "Dirty" | "Filthy"
  if self.state.condition.cleanliness > 0.8 then
    cleanlinessLevel = "Pristine"
  elseif self.state.condition.cleanliness > 0.6 then
    cleanlinessLevel = "Clean"
  elseif self.state.condition.cleanliness > 0.3 then
    cleanlinessLevel = "Dirty"
  else
    cleanlinessLevel = "Filthy"
  end

  -- Play inspection animation
  if self.animator then
    self.animator:playInspectionAnimation()
  end

  -- Get component conditions
  local componentConditions = {}
  for componentType, component in pairs(self.components) do
    -- Simplified component condition (would be more complex in real implementation)
    -- In a full implementation, each component would track its own condition
    componentConditions[componentType] = component.customData and component.customData.condition
      or self.state.condition.durability
  end

  -- Generate warnings
  local warnings = {}
  if durabilityPercent < 50 then
    table.insert(warnings, "Weapon durability is low")
  end
  if cleanlinessLevel == "Filthy" then
    table.insert(warnings, "Weapon requires cleaning")
  end

  return {
    chamberedRounds = self.state.ammunition.chambered,
    magazineRounds = self.state.ammunition.magazine,
    reserveRounds = self.state.ammunition.reserve,
    durabilityPercent = durabilityPercent,
    cleanlinessLevel = cleanlinessLevel,
    componentConditions = componentConditions,
    maintenanceWarnings = #warnings > 0 and warnings or nil,
    displayDuration = 3.0,
    animationId = string.format("inspect_%s", self.category),
  }
end

-- ============================================================================
-- COMPONENT MANAGEMENT METHODS
-- ============================================================================

function WeaponEntity:attach(component: WeaponComponent, pointName: string): AttachResult
  -- Validate inputs
  if not component then
    return {
      success = false,
      attachmentPoint = nil,
      modifiedStats = nil,
      conflicts = nil,
    }
  end

  -- Find attachment point
  local attachmentPoint = self.attachmentPoints[pointName]
  if not attachmentPoint then
    return {
      success = false,
      attachmentPoint = nil,
      modifiedStats = nil,
      conflicts = nil,
    }
  end

  -- Check if point is occupied
  if attachmentPoint.occupied then
    return {
      success = false,
      attachmentPoint = pointName,
      modifiedStats = nil,
      conflicts = nil,
    }
  end

  -- Check compatibility
  local isCompatible = false
  for _, compatibleType in ipairs(attachmentPoint.compatibleTypes) do
    if component.type == compatibleType then
      isCompatible = true
      break
    end
  end

  if not isCompatible then
    return {
      success = false,
      attachmentPoint = pointName,
      modifiedStats = nil,
      conflicts = { component.type },
    }
  end

  -- Validate component with weapon
  local validationResult = component:validate(self)
  if not validationResult.isValid then
    return {
      success = false,
      attachmentPoint = pointName,
      modifiedStats = nil,
      conflicts = nil,
    }
  end

  -- Attach component
  local attachResult = component:onAttach(self, attachmentPoint)
  if attachResult.success then
    self.components[component.type] = component
    self:updateStatistics()
  end

  return attachResult
end

function WeaponEntity:detach(componentType: ComponentType): DetachResult
  local component = self.components[componentType]
  if not component then
    return {
      success = false,
      restoredStats = nil,
      dependentComponents = nil,
    }
  end

  -- Detach component
  local detachResult = component:onDetach(self)
  if detachResult.success then
    self.components[componentType] = nil
    self:updateStatistics()
  end

  return detachResult
end

function WeaponEntity:getComponent(componentType: ComponentType): WeaponComponent?
  return self.components[componentType]
end

-- ============================================================================
-- STATE MANAGEMENT METHODS
-- ============================================================================

function WeaponEntity:updateStatistics(): ()
  -- Start with base statistics
  self.currentStatistics = table.clone(self.baseStatistics)

  -- Apply component modifications
  for _, component in pairs(self.components) do
    self.currentStatistics = component:modifyStatistics(self.currentStatistics)
  end

  self.lastUpdateTime = os.clock()
end

function WeaponEntity:validateState(): ValidationResult
  -- Basic state validation
  if
    self.state.ammunition.chambered < 0
    or self.state.ammunition.magazine < 0
    or self.state.ammunition.reserve < 0
  then
    return {
      isValid = false,
      errorCode = "InvalidHitRegistration",
      severity = "Error",
      description = "Ammunition counts cannot be negative",
      suggestedAction = "Correct",
    }
  end

  if self.state.condition.durability < 0 or self.state.condition.durability > 1 then
    return {
      isValid = false,
      errorCode = "PhysicsViolation",
      severity = "Error",
      description = "Durability must be between 0 and 1",
      suggestedAction = "Correct",
    }
  end

  return {
    isValid = true,
    severity = "Warning",
    description = "Weapon state is valid",
    suggestedAction = "Allow",
  }
end

-- ============================================================================
-- UTILITY METHODS
-- ============================================================================

function WeaponEntity:canFire(): boolean
  return not self.state.modes.safety
    and not self.state.flags.isReloading
    and (self.state.ammunition.chambered > 0 or self.state.ammunition.magazine > 0)
    and (os.clock() - self.state.timing.lastFired) >= self.state.timing.cycleTime
end

function WeaponEntity:canReload(): boolean
  return not self.state.flags.isReloading
    and not self.state.flags.isFiring
    and self.state.ammunition.reserve > 0
    and (self.state.ammunition.magazine + self.state.ammunition.chambered)
      < self.state.ammunition.maxMagazine
end

function WeaponEntity:getEffectiveRange(): number
  return self.currentStatistics.range
end

function WeaponEntity:getAccuracyAtRange(range: number): number
  local baseAccuracy = self.currentStatistics.accuracy
  local effectiveRange = self:getEffectiveRange()

  if range <= effectiveRange then
    return baseAccuracy
  else
    -- Accuracy degrades beyond effective range
    local degradationFactor = range / effectiveRange
    return baseAccuracy / degradationFactor
  end
end

function WeaponEntity:_calculateRecoil(): Vector3
  local recoilStats = self.currentStatistics.recoil
  local vertical = recoilStats.vertical * (0.8 + math.random() * 0.4) -- ±20% variation
  local horizontal = recoilStats.horizontal * (math.random() - 0.5) * 2 -- Random left/right

  return {
    X = horizontal,
    Y = vertical,
    Z = 0,
  }
end

-- ============================================================================
-- WEAPON FACTORY FUNCTIONS
-- ============================================================================

function WeaponEntity.createAssaultRifle(config: { [string]: any }): Weapon
  local defaultStats: WeaponStatistics = {
    damage = {
      baseDamage = 35,
      headMultiplier = 2.0,
      chestMultiplier = 1.0,
      limbMultiplier = 0.8,
      falloffStart = 50,
      falloffEnd = 150,
      minimumDamage = 20,
    },
    accuracy = 0.85,
    range = 400,
    fireRate = 650,
    muzzleVelocity = 900,
    recoil = {
      vertical = 0.3,
      horizontal = 0.1,
      pattern = {
        { X = 0, Y = 0.3, Z = 0 },
        { X = 0.1, Y = 0.4, Z = 0 },
        { X = -0.1, Y = 0.5, Z = 0 },
      },
      recovery = 0.8,
    },
    weight = 3.5,
    length = 80,
    reliability = 0.95,
    durabilityLoss = 0.0001,
    heatGeneration = 0.1,
    coolingRate = 0.05,
    reloadTime = {
      tactical = 2.5,
      empty = 3.2,
    },
    compatibleAmmo = { "556x45" },
    defaultAmmo = "556x45",
  }

  local weaponConfig = {
    id = config.id or string.format("ar_%d", os.clock() * 1000),
    name = config.name or "M4A1 Assault Rifle",
    category = "AssaultRifle",
    version = "1.0.0",
    baseStatistics = config.baseStatistics or defaultStats,
    initialReserve = config.initialReserve or 180,
    maxReserve = config.maxReserve or 300,
  }

  return WeaponEntity.new(weaponConfig)
end

function WeaponEntity.createSniperRifle(config: { [string]: any }): Weapon
  local defaultStats: WeaponStatistics = {
    damage = {
      baseDamage = 80,
      headMultiplier = 3.0,
      chestMultiplier = 1.2,
      limbMultiplier = 0.9,
      falloffStart = 100,
      falloffEnd = 800,
      minimumDamage = 60,
    },
    accuracy = 0.98,
    range = 800,
    fireRate = 60,
    muzzleVelocity = 850,
    recoil = {
      vertical = 1.2,
      horizontal = 0.3,
      pattern = { { X = 0, Y = 1.2, Z = 0 } },
      recovery = 0.6,
    },
    weight = 6.5,
    length = 120,
    reliability = 0.98,
    durabilityLoss = 0.00005,
    heatGeneration = 0.2,
    coolingRate = 0.03,
    reloadTime = {
      tactical = 3.5,
      empty = 4.2,
    },
    compatibleAmmo = { "762x51" },
    defaultAmmo = "762x51",
  }

  local weaponConfig = {
    id = config.id or string.format("sniper_%d", os.clock() * 1000),
    name = config.name or "M24 Sniper Rifle",
    category = "SniperRifle",
    version = "1.0.0",
    baseStatistics = config.baseStatistics or defaultStats,
    initialReserve = config.initialReserve or 40,
    maxReserve = config.maxReserve or 80,
  }

  return WeaponEntity.new(weaponConfig)
end

-- ============================================================================
-- MODULE EXPORT
-- ============================================================================

return WeaponEntity
